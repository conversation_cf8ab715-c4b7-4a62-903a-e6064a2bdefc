{"id": "558661747", "title": "集成平台2.0技术方案", "spaceKey": "", "content": "目录 UML说明 &nbsp; https://www.visual-paradigm.com/cn/guide/uml-unified-modeling-language/what-is-uml/ 一、背景说明 iPaaS（Integration Platform as a Service）是一个基于云的集成平台，用于实现系统间的数据流转和业务集成。本平台主要特点： 提供可视化的集成流设计器 支持多种集成节点类型 支持流程自由编排 基于effektif实现流程引擎 采用X6作为前端流程设计框架 需求文档： 集成平台-编排集成流 - 深圳研发中心 - 纷享wiki UI 二、架构图 2.1 系统架构图 原图地址 &nbsp; https://365.kdocs.cn/view/l/ccmh4yDjHXLZ?from=docs 2.2 技术架构图 原始链接 https://365.kdocs.cn/view/l/cspzY4lnO93q 技术架构图说明： 三、模块划分 1. 前端模块 集成流设计器 流程可视化设计 节点配置功能 集成流管理功能 节点参数配置 2. 接入层模块 Nginx 路由 安全性：ip白名单，并发控制，推送时派发的token&nbsp; 负载均衡：使用配置中心的ea 分组路由。 CEP 路由。&nbsp; &nbsp;Key统一调整为ERPDSS，三个环境使用同一个key。 3. 集成平台基座 框架服务。封装和使用其它框架代码，如effektif, 聚合框架等。 核心服务。数据同步主过程处理。 基础服务。除了数据流过集成流以外的其它服务。 4. &nbsp;数据层模块 PostgreSQL MongoDB RocketMQ Redis ClickHouse 四、流程图 4.1 集成流执行流程图 五、时序图 集成流编排(节点执行顺序) 六、集成平台基座功能说明 6.1 核心服务 集成流管理。准备好上下文和触发集成流的数据，启动集成流。 租户节点管理 节点定义。实现集成流的节点必须要遵循的规范。 连接器节点管理。 管理访问外部系统的处理节点。 业务节点管理。除开连接器节点的其它类型节点。 trigger节点管理。获取触发集成流执行的数据。 Task服务 Web服务 鉴权服务 限速服务 容错服务 日志管理 监控告警 模板管理 对账服务 6.2 框架服务 effektif引擎接口封装。 聚合分发框架。 6.4 集成流定义json示例 Emacs solid true 6.5 集成流显示效果示例 流程说明: jc1和jc2是两个处理节点 6.6 集成流示例的java处理代码 java RDark solid true true executeCompleteWorkflow(String workflowJson) { Map result = new HashMap (); WorkflowInstance instance; System.out.println(\"executeCompleteWorkflow\"); try { TestConfiguration configuration = createConfiguration(); WorkflowEngine engine = configuration.getWorkflowEngine(); System.out.println(\"print engine: \"+engine); WorkflowExecutionListener listener = new CustomWorkflowExecutionListener(); ((WorkflowEngineImpl)engine).addWorkflowExecutionListener(listener); ExecutableWorkflow workflow = new DefaultJsonStreamMapper().readString(workflowJson, ExecutableWorkflow.class); Deployment deployment = engine.deployWorkflow(workflow); result.put(\"deployStatus\", \"success\"); System.out.println(\"after deployWorkflow\"); TriggerInstance trigger = new TriggerInstance(\"testTenantid\").traceId(\"testtraceid\").workflowId(workflow.getId()); Map conditionMap = Maps.newHashMap(); conditionMap.put(\"k1\", \"value1\"); conditionMap.put(\"k2\", \"value2\"); instance = engine.start(trigger.data(conditionMap)); // 2. 启动工作流实例 String instanceId = instance.getId().getInternal(); System.out.println(\"print instanceId: \"+instanceId); result.put(\"instanceId\", instanceId); result.put(\"startStatus\", \"success\"); } catch (Exception e) { result.put(\"error\", e.getMessage()); System.out.println(e); } return result; } ]]> 七. &nbsp;集成平台基座子功能详细设计-集成流管理 子模块的功能描述 时序图和架构图 关键的数据结构和方法描述（类图，表结构设计） 核心场景测试用例-包括功能和压力测试 准备好上下文和触发集成流的数据，启动集成流。 设置元数据：数据的描述metadata，比如数据来源，traceid, tenantid，当前租户的级别，是否每一步都要缓存数据，是否做全流程节点状态监控等。 切割数据：(1)对于批量数据，拆解为单条，在送给集成流。(2)如果一条数据同时启动多个集成流。需要深度拷贝。 版本管理。老用户使用老版本，新用户使用新版本，迁移老用户到新版本需要连接器开发者手工触发，平台提供工具。 后面的版本再考虑。 选中部分节点重放请求。后面版本在考虑。 八. &nbsp;集成平台基座子功能详细设计-租户节点管理 1.节点定义 要实现一个新的节点，必须实现下面两个类。 类图1：集成流页面配置类 类图2：集成流处理节点类 2.连接器节点管理 trigger和action节点 一个集成流是一个trigger+一个或多个action组成。集成流的第一个节点只能是trigger节点。 trigger节点管理 Trigger节点的作用是 获取数据，通过轮询，推送方式，监听MQ事件&nbsp; 获取外部数据。trigger节点是集成流中的一个节点，有两种策略执行trigger节点。 策略1：启动集成流，顺序执行，第一个节点就是trigger节点，从trigger节点顺序往下执行。 策略2：trigger节点挑出来，拿到数据后，在启动集成流，执行其它节点。 策略1更容易理解，所见即所得，但是极难实现。 因此选择策略2。用户启动集成流以后，trigger节点要统一注册，然后在effektif之外执行轮询和接收推送事件。等数据到达，再启动集成流 。 策略1的问题：对于轮询场景，可以定时任务-&gt;启动集成流-&gt;执行轮询，大多数轮询没数据， 但是也启动了集成流。 对于监听事件的场景，问题更大了。 启动集成流&rarr;监听 要么一直卡住集成流，要么频繁启动集成流进行监听。 trigger接收的批量数据如何传给集成流？ trigger接收到的数据必须是数组，只支持JSON格式。类似下面的例子，zapier采用的是图2的方法。就是自动拆数据 策略1 策略2 策略2对用户的配置来说相对友好一些，但是隐藏一些语义，比如有3条数据，如果第2条失败，第3条要不要继续做。 轮询分页支持 (1)开关控制是否启用分页 (2)分页占位符支持url param, header, 和自定义函数代码引用。 (3)分页拉取结束条件为返回记录数为0。 (4)最多执行xx个分页，控制上限且页面展现。 执行逻辑流程如下图所示 现有连接器，是基于集成平台的接口要求，来提供接口。 新连接器体系：应该基于 原始接口操作 进行封装， 自动鉴权 ，用户 理解原始接口 ，则会使用连接器！ 改造思路： 【金山文档 | WPS云文档】&nbsp; https://365.kdocs.cn/l/ctkcWxeydAQT 3.节点之间 数据传递。 每个节点都能获取到前面所有节点产生的数据。依靠变量实现。 每执行到一个节点，把当前节点的输出，都append到变量。可以在集成流lister中实现这个append, 或者在各个处理节点中实现。但处理节点很难确保每个开发者(尤其涉及到外部开发者)都能正确实现。 4.调试功能 编辑状态的集成流才可以调试。 调试数据放在哪里，缓存多久？ 5. 业务节点管理(需要逐个按照功能设计) 除了连接器类型的节点，其它的都归类到业务节点。比如分支，循环，去重，排序。 顺序 分支 循环处理节点 字段转换 CRM函数处理节点 异常捕捉 重试组件 下面图分别是 anypoint和workato的重试组件，zapier没有找到重试组件。 九. &nbsp; 集成平台基座子功能详细设计-配置管理 十. &nbsp;集成平台基座子功能详细设计-鉴权管理 使用专业的，广泛认同的方式。 基于HTTP的常见授权方式主要有以下几种： Basic Auth（基本认证） 基本认证是最简单的HTTP授权方式，在HTTP请求头中使用 Authorization: Basic &lt;base64(username:password)&gt; 进行认证。 缺点：凭据明文（base64非加密），安全性较差，通常需配合HTTPS使用。 Bearer Token（令牌认证） 常见于OAuth 2.0中的授权方式，客户端获取到令牌（token）后，在HTTP请求头带上： Authorization: Bearer &lt;token&gt; 。 优点：可设置有效期、权限范围，令牌泄漏危害小于用户名密码。 API Key（API密钥认证） 客户端向服务端申请API Key，访问接口时将API Key放在请求头/URL参数/请求体中，如 Authorization: ApiKey &lt;key&gt; 或 ?api_key=xxx 。 多用于B2B服务，但容易被截获，适合简单场景。 OAuth 2.0 一种开放授权协议，主要用于第三方应用访问用户受保护资源。 授权流程较复杂，常用授权码模式、简化模式、密码模式、客户端凭证模式等。 通过Bearer Token实现接口访问权限控制。 JWT（JSON Web Token） JWT是一种基于Token的授权方式，通过签发一个数字签名的Token，客户端保存后每次请求携带。 一般在Header中： Authorization: Bearer &lt;jwt_token&gt; 。 优势：无状态，服务端不需保存会话信息，既可用作认证又可用作授权。 Digest Auth（摘要认证） 比Basic安全，采用哈希算法加密密码，不直接发送明文密码，仍需注意防护中间人攻击。 十一. &nbsp;集成平台基座子功能详细设计-限速管理 十二. &nbsp;集成平台基座子功能详细设计-task任务管理 十三. &nbsp;集成平台基座子功能详细设计-web服务管理 十四. &nbsp;集成平台基座子功能详细设计-容错管理 十五、系统设计的规范性 1. 序列化规范 1.1 序列化工具选择 统一使用Gson作为序列化和反序列化工具，原因如下： 性能对比 * Gson：中等性能，内存占用适中 * Jackson：高性能，但内存占用较大 * Fastjson：高性能，但存在安全漏洞 * Protobuf：最高性能，但需要预编译 功能特性对比 * Gson：* 支持泛型 * * 支持自定义序列化/反序列化 * * 支持循环引用 * * 支持null值处理 * * 支持注解 * * 字段顺序：默认按字母顺序排序，可通过自定义TypeAdapter保持原始顺序 * * null值处理：默认保留null值，可通过配置选择是否序列化null值 * Jackson：* 功能最全面 * * 配置复杂 * * 学习曲线陡峭 * * 字段顺序：默认按字母顺序排序，可通过@JsonPropertyOrder注解控制 * * null值处理：默认保留null值，可通过@JsonInclude注解控制 * Fastjson：* 配置简单 * * 存在安全风险 * * 维护不活跃 * * 字段顺序：默认按字段声明顺序，可通过@JSONField注解控制 * * null值处理：默认不序列化null值，可通过配置修改 * Protobuf：* 性能最好 * * 需要预编译 * * 不支持动态类型 * * 字段顺序：按字段编号顺序，固定不变 * * null值处理：不支持null值，使用默认值代替 使用规范 java RDark solid true true () { # @Override # public void write(JsonWriter out, YourClass value) throws IOException { # out.beginObject(); # // 按原始顺序写入字段 # out.name(\"field1\").value(value.getField1()); # out.name(\"field2\").value(value.getField2()); # // ... 其他字段 # out.endObject(); # } # @Override # public YourClass read(JsonReader in) throws IOException { # YourClass obj = new YourClass(); # in.beginObject(); # while (in.hasNext()) { # switch (in.nextName()) { # case \"field1\": # obj.setField1(in.nextString()); # break; # case \"field2\": # obj.setField2(in.nextString()); # break; # // ... 其他字段 # } # } # in.endObject(); # return obj; # } # }) # .create(); # // 序列化 # String json = gson.toJson(object); # // 反序列化 # Object obj = gson.fromJson(json, Object.class); # ]]> 2. 数值处理规范 2.1 小数位数规范 默认规则 * 所有数值类型默认保留6位小数 * 超过6位小数自动四舍五入 * 禁止使用科学计数法表示 特殊处理 * 金额计算：保留2位小数 * 百分比：保留2位小数 * 经纬度：保留6位小数 * 科学计算：保留6位小数 实现示例 java RDark solid true false 2.2 数值类型规范 金额类型 * 使用BigDecimal * 禁止使用double/float * 统一使用String构造方法 * ```java * // 正确示例 * BigDecimal amount = new BigDecimal(&quot;123.45&quot;); // 错误示例 BigDecimal amount = new BigDecimal(123.45); // 可能产生精度问题 ``` 百分比类型 * 使用BigDecimal * 范围：0-100 * 默认保留2位小数 java RDark solid true false 经纬度类型 * 使用BigDecimal * 经度范围：-180到180 * 纬度范围：-90到90 * 保留6位小数 java RDark solid true false = 0 && # * longitude.compareTo(new BigDecimal(\"180\")) = 0 && # * latitude.compareTo(new BigDecimal(\"90\")) 3. 异常处理规范 什么时候抛异常，什么时候返回错误码 3.1 异常分类 业务异常 * 继承RuntimeException * 包含错误码和错误信息 * 用于业务逻辑异常 系统异常 * 继承RuntimeException * 用于系统级异常 * 需要记录详细日志 3.2 异常处理示例 4. 记录类型规范 所有企业:ALL 所有连接器:ALL 所有对象：ALL 5. 测试规范 后台项目内测试 命名规范： 单元测试类: &nbsp; *Test.java &nbsp; (例如: &nbsp; UserServiceTest.java ) 集成测试类: &nbsp; *IT.java &nbsp; (例如: &nbsp; UserServiceIT.java ) 单元测试： JUnit 5：测试框架 Mockito：用于创建模拟对象 AssertJ：提供流畅的断言API 集成测试： 常用注解 @SpringBootTest ：加载完整的Spring应用程序上下文 @DataJpaTest ：用于测试JPA组件 @MockBean ：在Spring上下文中添加Mockito模拟 @AutoConfigureMockMvc： 模拟 HTTP 请求和响应。 CI自动化：Maven Surefire插件仅执行 *Test.java，不执行*IT.java 前端项目内测试 功能测试 API测试：使用公司私有部署的Apifox， Apifox 十、资源隔离和限制 1. 速度限制 限速模型为 源数据-&gt;过滤组件-&gt;连接器1-&gt;连接器2-&gt;连接器3 连接器1每分钟处理100条数据，连接器2每分钟处理50条数据，连接器3每分钟处理200条数据. 源数据每分钟发送多少条数据？ 限速策略待补充: 2. 数据大小限制 从外部读到的数据，单词不超过几M ? 打印的日志，单词不超过几M 3. 计算资源隔离 1.1 CPU资源隔离 cpu不做隔离 1.2 内存资源隔离 2. 存储资源隔离 2.1 数据存储隔离 2.2 缓存隔离 十一、系统容量管理 1. 扩容架构图 十二、系统安全性 1. 认证与授权 1.1 用户认证 认证方式 * OAuth2.0认证 * JWT令牌认证 * 双因素认证（2FA） * SSO单点登录 令牌管理 * 访问令牌有效期：2小时 * 刷新令牌有效期：7天 * 令牌自动续期 * 令牌撤销机制 1.2 权限控制 权限模型 * RBAC（基于角色的访问控制） * 集成流管理权限 * 连接器管理权限 * 数据访问权限 * 系统配置权限 1.3 模块功能说明 认证流程 2. 数据安全 2.1 数据加密 2.2 数据隔离 多租户数据隔离 3. 通信安全 3.1 API安全 接口安全* 接口认证 * 请求签名 * 防重放攻击 * 限流控制 3.2 连接器安全 连接器认证 * 支持多种认证方式 * 凭证加密存储 * 定期轮换 * 访问控制 数据传输安全 * 加密传输 * 数据验证 * 完整性校验 * 防篡改机制 4. 审计与监控 4.1 安全审计 审计内容* 用户操作日志 * 系统事件日志 * 安全事件日志 * 访问记录 4.2 安全监控 监控指标 * 认证失败次数 * 异常访问次数 * 系统资源使用 * 安全事件统计 告警机制 * 实时告警 * 告警级别 * 告警通知 * 告警处理 十三、系统高可靠性 十四、系统对账 十五、面向用户的监控 1. 监控架构 2. 监控维度 2.1 业务监控 2.2 性能监控 3. 监控指标 INLINE window.difyChatbotConfig = { token: \"T7LZV1J8Uy7ldXKf\", baseUrl: \"https://dify.firstshare.cn\", systemVariables: {}, userVariables: {}, }; #dify-chatbot-bubble-button { background-color: #1c64f2 !important; } #dify-chatbot-bubble-window { position: fixed !important; right: 40px !important; /* 这里如果要和按钮对齐可调整 */ bottom: 40px !important; z-index: 99999 !important; width: 32rem !important; height: 60rem !important; box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12); } ]]> INLINE ▼ 目录（双击收起） document.addEventListener('DOMContentLoaded', function(){ // 克隆目录（假设页面有class=\"toc-macro\"的目录） var origToc = document.querySelector('.toc-macro'); if(origToc) { var clonedToc = origToc.cloneNode(true); clonedToc.style.margin = '0'; document.getElementById('my_toc_content').appendChild(clonedToc); } // 拖拽 var tocBox = document.getElementById('my_floating_toc'); var handle = document.getElementById('my_drag_handle'); var offsetX, offsetY, dragging = false; handle.onmousedown = function(e) { dragging = true; offsetX = e.clientX - tocBox.offsetLeft; offsetY = e.clientY - tocBox.offsetTop; document.onmousemove = function(e) { if (dragging) { tocBox.style.left = (e.clientX - offsetX) + 'px'; tocBox.style.top = (e.clientY - offsetY) + 'px'; tocBox.style.right = 'auto'; tocBox.style.bottom = 'auto'; } }; document.onmouseup = function() { dragging = false; document.onmousemove = null; document.onmouseup = null; }; }; // 收起/展开 var content = document.getElementById('my_toc_content'); var foldIcon = document.getElementById('my_fold_icon'); var isFolded = false; handle.ondblclick = function(){ isFolded = !isFolded; if(isFolded){ content.style.display = 'none'; foldIcon.style.transform = \"rotate(-90deg)\"; tocBox.style.borderRadius = \"12px 12px 0 0\"; }else{ content.style.display = 'block'; foldIcon.style.transform = \"rotate(0deg)\"; tocBox.style.borderRadius = \"12px\"; } }; // 悬停高亮 tocBox.onmouseenter = function(){ this.style.boxShadow = \"0 8px 32px rgba(0,82,204,0.17)\"; } tocBox.onmouseleave = function(){ this.style.boxShadow = \"0 8px 24px rgba(0,0,0,0.15)\"; } }); ]]>", "contentMarkdown": "目录\n\n  \n\nUML说明  [https://www.visual-paradigm.com/cn/guide/uml-unified-modeling-language/what-is-uml/](https://www.visual-paradigm.com/cn/guide/uml-unified-modeling-language/what-is-uml/)\n\n## 一、背景说明\n\niPaaS（Integration Platform as a Service）是一个基于云的集成平台，用于实现系统间的数据流转和业务集成。本平台主要特点：\n\n1.  提供可视化的集成流设计器\n2.  支持多种集成节点类型\n3.  支持流程自由编排\n4.  基于effektif实现流程引擎\n5.  采用X6作为前端流程设计框架\n\n需求文档：[集成平台-编排集成流 - 深圳研发中心 - 纷享wiki](https://wiki.firstshare.cn/pages/viewpage.action?pageId=422314522)\n\nUI\n\n## 二、架构图\n\n### 2.1 系统架构图\n\n原图地址  [https://365.kdocs.cn/view/l/ccmh4yDjHXLZ?from=docs](https://365.kdocs.cn/view/l/ccmh4yDjHXLZ?from=docs)\n\n### 2.2 技术架构图\n\n原始链接 [https://365.kdocs.cn/view/l/cspzY4lnO93q](https://365.kdocs.cn/view/l/cspzY4lnO93q)\n\n技术架构图说明：\n\n## 三、模块划分\n\n### 1\\. 前端模块\n\n-   集成流设计器\n    -   流程可视化设计\n    -   节点配置功能\n    -   集成流管理功能\n    -   节点参数配置\n\n### 2\\. 接入层模块\n\n-   Nginx 路由\n    -   安全性：ip白名单，并发控制，推送时派发的token \n    -   负载均衡：使用配置中心的ea 分组路由。\n-   CEP 路由。   Key统一调整为ERPDSS，三个环境使用同一个key。\n\n### 3\\. 集成平台基座\n\n-   框架服务。封装和使用其它框架代码，如effektif, 聚合框架等。\n-   核心服务。数据同步主过程处理。\n-   基础服务。除了数据流过集成流以外的其它服务。\n\n### 4\\.  数据层模块\n\n-   PostgreSQL\n-   MongoDB\n-   RocketMQ\n-   Redis\n-   ClickHouse\n\n## 四、流程图\n\n### 4.1 集成流执行流程图\n\n## 五、时序图\n\n### 集成流编排(节点执行顺序)\n\n## 六、集成平台基座功能说明\n\n### 6.1 核心服务\n\n-   集成流管理。准备好上下文和触发集成流的数据，启动集成流。\n-   租户节点管理\n    -   节点定义。实现集成流的节点必须要遵循的规范。\n    -   连接器节点管理。 管理访问外部系统的处理节点。\n    -   业务节点管理。除开连接器节点的其它类型节点。\n-   trigger节点管理。获取触发集成流执行的数据。\n-   Task服务\n-   Web服务\n-   鉴权服务\n-   限速服务\n-   容错服务\n-   日志管理\n-   监控告警\n-   模板管理\n-   对账服务\n\n### 6.2 框架服务\n\n-   effektif引擎接口封装。\n-   聚合分发框架。\n\n### 6.4 集成流定义json示例\n\n```\n\n```\n\n### 6.5 集成流显示效果示例\n\n流程说明: jc1和jc2是两个处理节点\n\n  \n\n### 6.6 集成流示例的java处理代码\n\n```java\n executeCompleteWorkflow(String workflowJson) { Map result = new HashMap<>(); WorkflowInstance instance; System.out.println(\"executeCompleteWorkflow\"); try { TestConfiguration configuration = createConfiguration(); WorkflowEngine engine = configuration.getWorkflowEngine(); System.out.println(\"print engine: \"+engine); WorkflowExecutionListener listener = new CustomWorkflowExecutionListener(); ((WorkflowEngineImpl)engine).addWorkflowExecutionListener(listener); ExecutableWorkflow workflow = new DefaultJsonStreamMapper().readString(workflowJson, ExecutableWorkflow.class); Deployment deployment = engine.deployWorkflow(workflow); result.put(\"deployStatus\", \"success\"); System.out.println(\"after deployWorkflow\"); TriggerInstance trigger = new TriggerInstance(\"testTenantid\").traceId(\"testtraceid\").workflowId(workflow.getId()); Map conditionMap = Maps.newHashMap(); conditionMap.put(\"k1\", \"value1\"); conditionMap.put(\"k2\", \"value2\"); instance = engine.start(trigger.data(conditionMap)); // 2. 启动工作流实例 String instanceId = instance.getId().getInternal(); System.out.println(\"print instanceId: \"+instanceId); result.put(\"instanceId\", instanceId); result.put(\"startStatus\", \"success\"); } catch (Exception e) { result.put(\"error\", e.getMessage()); System.out.println(e); } return result; }         ]]>\n```\n\n  \n\n## 七.  集成平台基座子功能详细设计-集成流管理\n\n1.  子模块的功能描述\n2.  时序图和架构图\n3.  关键的数据结构和方法描述（类图，表结构设计）\n4.  核心场景测试用例-包括功能和压力测试\n\n准备好上下文和触发集成流的数据，启动集成流。\n\n-   -   设置元数据：数据的描述metadata，比如数据来源，traceid, tenantid，当前租户的级别，是否每一步都要缓存数据，是否做全流程节点状态监控等。\n    -   切割数据：(1)对于批量数据，拆解为单条，在送给集成流。(2)如果一条数据同时启动多个集成流。需要深度拷贝。\n    -   版本管理。老用户使用老版本，新用户使用新版本，迁移老用户到新版本需要连接器开发者手工触发，平台提供工具。 后面的版本再考虑。\n    -   选中部分节点重放请求。后面版本在考虑。\n\n## 八.  集成平台基座子功能详细设计-租户节点管理\n\n### 1.节点定义\n\n要实现一个新的节点，必须实现下面两个类。\n\n类图1：集成流页面配置类\n\n类图2：集成流处理节点类\n\n### 2.连接器节点管理\n\n-   #### trigger和action节点\n    \n\n一个集成流是一个trigger+一个或多个action组成。集成流的第一个节点只能是trigger节点。\n\n-   #### trigger节点管理\n    \n\nTrigger节点的作用是 获取数据，通过轮询，推送方式，监听MQ事件  获取外部数据。trigger节点是集成流中的一个节点，有两种策略执行trigger节点。\n\n-   -   策略1：启动集成流，顺序执行，第一个节点就是trigger节点，从trigger节点顺序往下执行。\n    -   策略2：trigger节点挑出来，拿到数据后，在启动集成流，执行其它节点。\n\n策略1更容易理解，所见即所得，但是极难实现。因此选择策略2。用户启动集成流以后，trigger节点要统一注册，然后在effektif之外执行轮询和接收推送事件。等数据到达，再启动集成流 。策略1的问题：对于轮询场景，可以定时任务->启动集成流->执行轮询，大多数轮询没数据， 但是也启动了集成流。对于监听事件的场景，问题更大了。 启动集成流→监听 要么一直卡住集成流，要么频繁启动集成流进行监听。\n\n-   #### trigger接收的批量数据如何传给集成流？\n    \n\ntrigger接收到的数据必须是数组，只支持JSON格式。类似下面的例子，zapier采用的是图2的方法。就是自动拆数据\n\n策略1\n\n策略2\n\n策略2对用户的配置来说相对友好一些，但是隐藏一些语义，比如有3条数据，如果第2条失败，第3条要不要继续做。\n\n  \n\n-   #### 轮询分页支持\n    \n\n(1)开关控制是否启用分页\n\n(2)分页占位符支持url param, header, 和自定义函数代码引用。\n\n(3)分页拉取结束条件为返回记录数为0。\n\n(4)最多执行xx个分页，控制上限且页面展现。\n\n执行逻辑流程如下图所示\n\n  \n\n现有连接器，是基于集成平台的接口要求，来提供接口。\n\n新连接器体系：应该基于**原始接口操作**进行封装，**自动鉴权**，用户**理解原始接口**，则会使用连接器！\n\n改造思路：\n\n【金山文档 | WPS云文档】   \n[https://365.kdocs.cn/l/ctkcWxeydAQT](https://365.kdocs.cn/l/ctkcWxeydAQT)\n\n### 3.节点之间数据传递。\n\n每个节点都能获取到前面所有节点产生的数据。依靠变量实现。\n\n每执行到一个节点，把当前节点的输出，都append到变量。可以在集成流lister中实现这个append, 或者在各个处理节点中实现。但处理节点很难确保每个开发者(尤其涉及到外部开发者)都能正确实现。\n\n  \n\n### 4.调试功能\n\n编辑状态的集成流才可以调试。\n\n调试数据放在哪里，缓存多久？\n\n### 5.业务节点管理(需要逐个按照功能设计)\n\n除了连接器类型的节点，其它的都归类到业务节点。比如分支，循环，去重，排序。\n\n-   顺序\n-   分支\n-   循环处理节点\n-   字段转换\n\n-   CRM函数处理节点\n-   异常捕捉\n\n-   重试组件\n\n下面图分别是 anypoint和workato的重试组件，zapier没有找到重试组件。\n\n## 九.  集成平台基座子功能详细设计-配置管理\n\n## 十.  集成平台基座子功能详细设计-鉴权管理\n\n使用专业的，广泛认同的方式。\n\n基于HTTP的常见授权方式主要有以下几种：\n\n1.  **Basic Auth（基本认证）**\n    \n    -   基本认证是最简单的HTTP授权方式，在HTTP请求头中使用`Authorization: Basic <base64(username:password)>`进行认证。\n    -   缺点：凭据明文（base64非加密），安全性较差，通常需配合HTTPS使用。\n2.  **Bearer Token（令牌认证）**\n    \n    -   常见于OAuth 2.0中的授权方式，客户端获取到令牌（token）后，在HTTP请求头带上：`Authorization: Bearer <token>`。\n    -   优点：可设置有效期、权限范围，令牌泄漏危害小于用户名密码。\n3.  **API Key（API密钥认证）**\n    \n    -   客户端向服务端申请API Key，访问接口时将API Key放在请求头/URL参数/请求体中，如`Authorization: ApiKey <key>`或`?api_key=xxx`。\n    -   多用于B2B服务，但容易被截获，适合简单场景。\n4.  **OAuth 2.0**\n    \n    -   一种开放授权协议，主要用于第三方应用访问用户受保护资源。\n    -   授权流程较复杂，常用授权码模式、简化模式、密码模式、客户端凭证模式等。\n    -   通过Bearer Token实现接口访问权限控制。\n5.  **JWT（JSON Web Token）**\n    \n    -   JWT是一种基于Token的授权方式，通过签发一个数字签名的Token，客户端保存后每次请求携带。\n    -   一般在Header中：`Authorization: Bearer <jwt_token>`。\n    -   优势：无状态，服务端不需保存会话信息，既可用作认证又可用作授权。\n6.  **Digest Auth（摘要认证）**\n    \n    -   比Basic安全，采用哈希算法加密密码，不直接发送明文密码，仍需注意防护中间人攻击。\n\n## 十一.  集成平台基座子功能详细设计-限速管理\n\n## 十二.  集成平台基座子功能详细设计-task任务管理\n\n## 十三.  集成平台基座子功能详细设计-web服务管理\n\n## 十四.  集成平台基座子功能详细设计-容错管理\n\n## 十五、系统设计的规范性\n\n### 1\\. 序列化规范\n\n#### 1.1 序列化工具选择\n\n统一使用Gson作为序列化和反序列化工具，原因如下：\n\n1.  性能对比\n2.  \\* Gson：中等性能，内存占用适中\n3.  \\* Jackson：高性能，但内存占用较大\n4.  \\* Fastjson：高性能，但存在安全漏洞\n5.  \\* Protobuf：最高性能，但需要预编译\n6.  功能特性对比\n7.  \\* Gson：\\* 支持泛型\n8.  \\* \\* 支持自定义序列化/反序列化\n9.  \\* \\* 支持循环引用\n10.  \\* \\* 支持null值处理\n11.  \\* \\* 支持注解\n12.  \\* \\* 字段顺序：默认按字母顺序排序，可通过自定义TypeAdapter保持原始顺序\n13.  \\* \\* null值处理：默认保留null值，可通过配置选择是否序列化null值\n14.  \\* Jackson：\\* 功能最全面\n15.  \\* \\* 配置复杂\n16.  \\* \\* 学习曲线陡峭\n17.  \\* \\* 字段顺序：默认按字母顺序排序，可通过@JsonPropertyOrder注解控制\n18.  \\* \\* null值处理：默认保留null值，可通过@JsonInclude注解控制\n19.  \\* Fastjson：\\* 配置简单\n20.  \\* \\* 存在安全风险\n21.  \\* \\* 维护不活跃\n22.  \\* \\* 字段顺序：默认按字段声明顺序，可通过@JSONField注解控制\n23.  \\* \\* null值处理：默认不序列化null值，可通过配置修改\n24.  \\* Protobuf：\\* 性能最好\n25.  \\* \\* 需要预编译\n26.  \\* \\* 不支持动态类型\n27.  \\* \\* 字段顺序：按字段编号顺序，固定不变\n28.  \\* \\* null值处理：不支持null值，使用默认值代替\n29.  使用规范\n30.    \n    \n    ```java\n    () { #         @Override #         public void write(JsonWriter out, YourClass value) throws IOException { #             out.beginObject(); #             // 按原始顺序写入字段 #             out.name(\"field1\").value(value.getField1()); #             out.name(\"field2\").value(value.getField2()); #             // ... 其他字段 #             out.endObject(); #         } #         @Override #         public YourClass read(JsonReader in) throws IOException { #             YourClass obj = new YourClass(); #             in.beginObject(); #             while (in.hasNext()) { #                 switch (in.nextName()) { #                     case \"field1\": #                         obj.setField1(in.nextString()); #                         break; #                     case \"field2\": #                         obj.setField2(in.nextString()); #                         break; #                     // ... 其他字段 #                 } #             } #             in.endObject(); #             return obj; #         } #     }) #     .create(); # // 序列化 # String json = gson.toJson(object); # // 反序列化 # Object obj = gson.fromJson(json, Object.class); # ]]>\n    ```\n    \n      \n    \n\n### 2\\. 数值处理规范\n\n#### 2.1 小数位数规范\n\n1.  默认规则\n2.  \\* 所有数值类型默认保留6位小数\n3.  \\* 超过6位小数自动四舍五入\n4.  \\* 禁止使用科学计数法表示\n5.  特殊处理\n6.  \\* 金额计算：保留2位小数\n7.  \\* 百分比：保留2位小数\n8.  \\* 经纬度：保留6位小数\n9.  \\* 科学计算：保留6位小数\n10.  实现示例\n11.    \n    \n    ```java\n    \n    ```\n    \n      \n    \n\n#### 2.2 数值类型规范\n\n1.  金额类型\n2.  \\* 使用BigDecimal\n3.  \\* 禁止使用double/float\n4.  \\* 统一使用String构造方法\n5.  \\* \\`\\`\\`java\n6.  \\* // 正确示例\n7.  \\* BigDecimal amount = new BigDecimal(\"123.45\");\n8.  // 错误示例\n9.  BigDecimal amount = new BigDecimal(123.45); // 可能产生精度问题\n10.  \\`\\`\\`\n11.  百分比类型\n12.  \\* 使用BigDecimal\n13.  \\* 范围：0-100\n14.  \\* 默认保留2位小数\n    \n    ```java\n    \n    ```\n    \n      \n    \n15.  经纬度类型\n16.  \\* 使用BigDecimal\n17.  \\* 经度范围：-180到180\n18.  \\* 纬度范围：-90到90\n19.  \\* 保留6位小数\n    \n    ```java\n    = 0 && # *          longitude.compareTo(new BigDecimal(\"180\")) <= 0 && # *          latitude.compareTo(new BigDecimal(\"-90\")) >= 0 && # *          latitude.compareTo(new BigDecimal(\"90\")) <= 0; # * } # * ]]>\n    ```\n    \n      \n    \n\n### 3\\. 异常处理规范\n\n什么时候抛异常，什么时候返回错误码\n\n#### 3.1 异常分类\n\n1.  业务异常\n2.  \\* 继承RuntimeException\n3.  \\* 包含错误码和错误信息\n4.  \\* 用于业务逻辑异常\n5.  系统异常\n6.  \\* 继承RuntimeException\n7.  \\* 用于系统级异常\n8.  \\* 需要记录详细日志\n\n#### 3.2 异常处理示例\n\n### 4\\. 记录类型规范\n\n所有企业:ALL\n\n所有连接器:ALL\n\n所有对象：ALL\n\n### 5\\. 测试规范\n\n#### 后台项目内测试\n\n命名规范：\n\n-   单元测试类: `*Test.java` (例如: `UserServiceTest.java`)\n-   集成测试类: `*IT.java` (例如: `UserServiceIT.java`)\n\n单元测试：\n\n-   JUnit 5：测试框架\n-   Mockito：用于创建模拟对象\n-   AssertJ：提供流畅的断言API\n\n集成测试：\n\n常用注解\n\n-   `@SpringBootTest`：加载完整的Spring应用程序上下文\n-   `@DataJpaTest`：用于测试JPA组件\n-   `@MockBean`：在Spring上下文中添加Mockito模拟\n-   @AutoConfigureMockMvc：模拟 HTTP 请求和响应。\n\nCI自动化：Maven Surefire插件仅执行\\*Test.java，不执行\\*IT.java\n\n#### 前端项目内测试\n\n#### 功能测试\n\nAPI测试：使用公司私有部署的Apifox，[Apifox](https://apifox.firstshare.cn/web/)\n\n## 十、资源隔离和限制\n\n### 1\\. 速度限制\n\n限速模型为\n\n源数据->过滤组件->连接器1->连接器2->连接器3\n\n连接器1每分钟处理100条数据，连接器2每分钟处理50条数据，连接器3每分钟处理200条数据.  \n源数据每分钟发送多少条数据？\n\n限速策略待补充:\n\n### 2\\. 数据大小限制\n\n从外部读到的数据，单词不超过几M ?  \n打印的日志，单词不超过几M\n\n### 3\\. 计算资源隔离\n\n#### 1.1 CPU资源隔离\n\ncpu不做隔离\n\n#### 1.2 内存资源隔离\n\n### 2\\. 存储资源隔离\n\n#### 2.1 数据存储隔离\n\n#### 2.2 缓存隔离\n\n  \n\n## 十一、系统容量管理\n\n### 1\\. 扩容架构图\n\n## 十二、系统安全性\n\n### 1\\. 认证与授权\n\n#### 1.1 用户认证\n\n1.  认证方式\n2.  \\* OAuth2.0认证\n3.  \\* JWT令牌认证\n4.  \\* 双因素认证（2FA）\n5.  \\* SSO单点登录\n6.  令牌管理\n7.  \\* 访问令牌有效期：2小时\n8.  \\* 刷新令牌有效期：7天\n9.  \\* 令牌自动续期\n10.  \\* 令牌撤销机制\n\n#### 1.2 权限控制\n\n1.  权限模型\n2.  \\* RBAC（基于角色的访问控制）\n3.  \\* 集成流管理权限\n4.  \\* 连接器管理权限\n5.  \\* 数据访问权限\n6.  \\* 系统配置权限\n\n#### 1.3 模块功能说明\n\n1.  认证流程\n    \n\n### 2\\. 数据安全\n\n#### 2.1 数据加密\n\n#### 2.2 数据隔离\n\n1.  多租户数据隔离\n\n### 3\\. 通信安全\n\n#### 3.1 API安全\n\n1.  接口安全\\* 接口认证\n2.  \\* 请求签名\n3.  \\* 防重放攻击\n4.  \\* 限流控制\n\n#### 3.2 连接器安全\n\n1.  连接器认证\n2.  \\* 支持多种认证方式\n3.  \\* 凭证加密存储\n4.  \\* 定期轮换\n5.  \\* 访问控制\n6.  数据传输安全\n7.  \\* 加密传输\n8.  \\* 数据验证\n9.  \\* 完整性校验\n10.  \\* 防篡改机制\n\n### 4\\. 审计与监控\n\n#### 4.1 安全审计\n\n1.  审计内容\\* 用户操作日志\n2.  \\* 系统事件日志\n3.  \\* 安全事件日志\n4.  \\* 访问记录\n\n#### 4.2 安全监控\n\n1.  监控指标\n2.  \\* 认证失败次数\n3.  \\* 异常访问次数\n4.  \\* 系统资源使用\n5.  \\* 安全事件统计\n6.  告警机制\n7.  \\* 实时告警\n8.  \\* 告警级别\n9.  \\* 告警通知\n10.  \\* 告警处理\n\n## 十三、系统高可靠性\n\n## 十四、系统对账\n\n## 十五、面向用户的监控\n\n### 1\\. 监控架构\n\n### 2\\. 监控维度\n\n#### 2.1 业务监控\n\n#### 2.2 性能监控\n\n### 3\\. 监控指标\n\n  \n\n<!-- Confluence宏: html-bobswift -->\nINLINE window.difyChatbotConfig = { token: \"T7LZV1J8Uy7ldXKf\", baseUrl: \"https://dify.firstshare.cn\", systemVariables: {}, userVariables: {}, }; #dify-chatbot-bubble-button { background-color: #1c64f2 !important; } #dify-chatbot-bubble-window { position: fixed !important; right: 40px !important; /\\* 这里如果要和按钮对齐可调整 \\*/ bottom: 40px !important; z-index: 99999 !important; width: 32rem !important; height: 60rem !important; box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12); } \\]\\]>\n\n  \n\n<!-- Confluence宏: html-bobswift -->\nINLINE\n\n▼ 目录（双击收起）\n\ndocument.addEventListener('DOMContentLoaded', function(){ // 克隆目录（假设页面有class=\"toc-macro\"的目录） var origToc = document.querySelector('.toc-macro'); if(origToc) { var clonedToc = origToc.cloneNode(true); clonedToc.style.margin = '0'; document.getElementById('my\\_toc\\_content').appendChild(clonedToc); } // 拖拽 var tocBox = document.getElementById('my\\_floating\\_toc'); var handle = document.getElementById('my\\_drag\\_handle'); var offsetX, offsetY, dragging = false; handle.onmousedown = function(e) { dragging = true; offsetX = e.clientX - tocBox.offsetLeft; offsetY = e.clientY - tocBox.offsetTop; document.onmousemove = function(e) { if (dragging) { tocBox.style.left = (e.clientX - offsetX) + 'px'; tocBox.style.top = (e.clientY - offsetY) + 'px'; tocBox.style.right = 'auto'; tocBox.style.bottom = 'auto'; } }; document.onmouseup = function() { dragging = false; document.onmousemove = null; document.onmouseup = null; }; }; // 收起/展开 var content = document.getElementById('my\\_toc\\_content'); var foldIcon = document.getElementById('my\\_fold\\_icon'); var isFolded = false; handle.ondblclick = function(){ isFolded = !isFolded; if(isFolded){ content.style.display = 'none'; foldIcon.style.transform = \"rotate(-90deg)\"; tocBox.style.borderRadius = \"12px 12px 0 0\"; }else{ content.style.display = 'block'; foldIcon.style.transform = \"rotate(0deg)\"; tocBox.style.borderRadius = \"12px\"; } }; // 悬停高亮 tocBox.onmouseenter = function(){ this.style.boxShadow = \"0 8px 32px rgba(0,82,204,0.17)\"; } tocBox.onmouseleave = function(){ this.style.boxShadow = \"0 8px 24px rgba(0,0,0,0.15)\"; } }); \\]\\]>", "contentMarkdownSimple": "目录 UML说明   https://www.visual-paradigm.com/cn/guide/uml-unified-modeling-language/what-is-uml/ 一、背景说明 iPaaS（Integration Platform as a Service）是一个基于云的集成平台，用于实现系统间的数据流转和业务集成。本平台主要特点： 提供可视化的集成流设计器 支持多种集成节点类型 支持流程自由编排 基于effektif实现流程引擎 采用X6作为前端流程设计框架 需求文档： 集成平台-编排集成流 - 深圳研发中心 - 纷享wiki UI 二、架构图 2.1 系统架构图 原图地址   https://365.kdocs.cn/view/l/ccmh4yDjHXLZ?from=docs 2.2 技术架构图 原始链接 https://365.kdocs.cn/view/l/cspzY4lnO93q 技术架构图说明： 三、模块划分 1. 前端模块 集成流设计器 流程可视化设计 节点配置功能 集成流管理功能 节点参数配置 2. 接入层模块 Nginx 路由 安全性：ip白名单，并发控制，推送时派发的token  负载均衡：使用配置中心的ea 分组路由。 CEP 路由。   Key统一调整为ERPDSS，三个环境使用同一个key。 3. 集成平台基座 框架服务。封装和使用其它框架代码，如effektif, 聚合框架等。 核心服务。数据同步主过程处理。 基础服务。除了数据流过集成流以外的其它服务。 4.  数据层模块 PostgreSQL MongoDB RocketMQ Redis ClickHouse 四、流程图 4.1 集成流执行流程图 五、时序图 集成流编排(节点执行顺序) 六、集成平台基座功能说明 6.1 核心服务 集成流管理。准备好上下文和触发集成流的数据，启动集成流。 租户节点管理 节点定义。实现集成流的节点必须要遵循的规范。 连接器节点管理。 管理访问外部系统的处理节点。 业务节点管理。除开连接器节点的其它类型节点。 trigger节点管理。获取触发集成流执行的数据。 Task服务 Web服务 鉴权服务 限速服务 容错服务 日志管理 监控告警 模板管理 对账服务 6.2 框架服务 effektif引擎接口封装。 聚合分发框架。 6.4 集成流定义json示例 Emacs solid true 6.5 集成流显示效果示例 流程说明: jc1和jc2是两个处理节点 6.6 集成流示例的java处理代码 java RDark solid true true executeCompleteWorkflow(String workflowJson) { Map result = new HashMap (); WorkflowInstance instance; System.out.println(\"executeCompleteWorkflow\"); try { TestConfiguration configuration = createConfiguration(); WorkflowEngine engine = configuration.getWorkflowEngine(); System.out.println(\"print engine: \"+engine); WorkflowExecutionListener listener = new CustomWorkflowExecutionListener(); ((WorkflowEngineImpl)engine).addWorkflowExecutionListener(listener); ExecutableWorkflow workflow = new DefaultJsonStreamMapper().readString(workflowJson, ExecutableWorkflow.class); Deployment deployment = engine.deployWorkflow(workflow); result.put(\"deployStatus\", \"success\"); System.out.println(\"after deployWorkflow\"); TriggerInstance trigger = new TriggerInstance(\"testTenantid\").traceId(\"testtraceid\").workflowId(workflow.getId()); Map conditionMap = Maps.newHashMap(); conditionMap.put(\"k1\", \"value1\"); conditionMap.put(\"k2\", \"value2\"); instance = engine.start(trigger.data(conditionMap)); // 2. 启动工作流实例 String instanceId = instance.getId().getInternal(); System.out.println(\"print instanceId: \"+instanceId); result.put(\"instanceId\", instanceId); result.put(\"startStatus\", \"success\"); } catch (Exception e) { result.put(\"error\", e.getMessage()); System.out.println(e); } return result; } ]]&gt; 七.  集成平台基座子功能详细设计-集成流管理 子模块的功能描述 时序图和架构图 关键的数据结构和方法描述（类图，表结构设计） 核心场景测试用例-包括功能和压力测试 准备好上下文和触发集成流的数据，启动集成流。 设置元数据：数据的描述metadata，比如数据来源，traceid, tenantid，当前租户的级别，是否每一步都要缓存数据，是否做全流程节点状态监控等。 切割数据：(1)对于批量数据，拆解为单条，在送给集成流。(2)如果一条数据同时启动多个集成流。需要深度拷贝。 版本管理。老用户使用老版本，新用户使用新版本，迁移老用户到新版本需要连接器开发者手工触发，平台提供工具。 后面的版本再考虑。 选中部分节点重放请求。后面版本在考虑。 八.  集成平台基座子功能详细设计-租户节点管理 1.节点定义 要实现一个新的节点，必须实现下面两个类。 类图1：集成流页面配置类 类图2：集成流处理节点类 2.连接器节点管理 trigger和action节点 一个集成流是一个trigger+一个或多个action组成。集成流的第一个节点只能是trigger节点。 trigger节点管理 Trigger节点的作用是 获取数据，通过轮询，推送方式，监听MQ事件  获取外部数据。trigger节点是集成流中的一个节点，有两种策略执行trigger节点。 策略1：启动集成流，顺序执行，第一个节点就是trigger节点，从trigger节点顺序往下执行。 策略2：trigger节点挑出来，拿到数据后，在启动集成流，执行其它节点。 策略1更容易理解，所见即所得，但是极难实现。 因此选择策略2。用户启动集成流以后，trigger节点要统一注册，然后在effektif之外执行轮询和接收推送事件。等数据到达，再启动集成流 。 策略1的问题：对于轮询场景，可以定时任务-&gt;启动集成流-&gt;执行轮询，大多数轮询没数据， 但是也启动了集成流。 对于监听事件的场景，问题更大了。 启动集成流→监听 要么一直卡住集成流，要么频繁启动集成流进行监听。 trigger接收的批量数据如何传给集成流？ trigger接收到的数据必须是数组，只支持JSON格式。类似下面的例子，zapier采用的是图2的方法。就是自动拆数据 策略1 策略2 策略2对用户的配置来说相对友好一些，但是隐藏一些语义，比如有3条数据，如果第2条失败，第3条要不要继续做。 轮询分页支持 (1)开关控制是否启用分页 (2)分页占位符支持url param, header, 和自定义函数代码引用。 (3)分页拉取结束条件为返回记录数为0。 (4)最多执行xx个分页，控制上限且页面展现。 执行逻辑流程如下图所示 现有连接器，是基于集成平台的接口要求，来提供接口。 新连接器体系：应该基于 原始接口操作 进行封装， 自动鉴权 ，用户 理解原始接口 ，则会使用连接器！ 改造思路： 【金山文档 | WPS云文档】  https://365.kdocs.cn/l/ctkcWxeydAQT 3.节点之间 数据传递。 每个节点都能获取到前面所有节点产生的数据。依靠变量实现。 每执行到一个节点，把当前节点的输出，都append到变量。可以在集成流lister中实现这个append, 或者在各个处理节点中实现。但处理节点很难确保每个开发者(尤其涉及到外部开发者)都能正确实现。 4.调试功能 编辑状态的集成流才可以调试。 调试数据放在哪里，缓存多久？ 5. 业务节点管理(需要逐个按照功能设计) 除了连接器类型的节点，其它的都归类到业务节点。比如分支，循环，去重，排序。 顺序 分支 循环处理节点 字段转换 CRM函数处理节点 异常捕捉 重试组件 下面图分别是 anypoint和workato的重试组件，zapier没有找到重试组件。 九.   集成平台基座子功能详细设计-配置管理 十.  集成平台基座子功能详细设计-鉴权管理 使用专业的，广泛认同的方式。 基于HTTP的常见授权方式主要有以下几种： Basic Auth（基本认证） 基本认证是最简单的HTTP授权方式，在HTTP请求头中使用 Authorization: Basic &lt;base64(username:password)&gt; 进行认证。 缺点：凭据明文（base64非加密），安全性较差，通常需配合HTTPS使用。 Bearer Token（令牌认证） 常见于OAuth 2.0中的授权方式，客户端获取到令牌（token）后，在HTTP请求头带上： Authorization: Bearer &lt;token&gt; 。 优点：可设置有效期、权限范围，令牌泄漏危害小于用户名密码。 API Key（API密钥认证） 客户端向服务端申请API Key，访问接口时将API Key放在请求头/URL参数/请求体中，如 Authorization: ApiKey &lt;key&gt; 或 ?api_key=xxx 。 多用于B2B服务，但容易被截获，适合简单场景。 OAuth 2.0 一种开放授权协议，主要用于第三方应用访问用户受保护资源。 授权流程较复杂，常用授权码模式、简化模式、密码模式、客户端凭证模式等。 通过Bearer Token实现接口访问权限控制。 JWT（JSON Web Token） JWT是一种基于Token的授权方式，通过签发一个数字签名的Token，客户端保存后每次请求携带。 一般在Header中： Authorization: Bearer &lt;jwt_token&gt; 。 优势：无状态，服务端不需保存会话信息，既可用作认证又可用作授权。 Digest Auth（摘要认证） 比Basic安全，采用哈希算法加密密码，不直接发送明文密码，仍需注意防护中间人攻击。 十一.  集成平台基座子功能详细设计-限速管理 十二.  集成平台基座子功能详细设计-task任务管理 十三.  集成平台基座子功能详细设计-web服务管理 十四.  集成平台基座子功能详细设计-容错管理 十五、系统设计的规范性 1. 序列化规范 1.1 序列化工具选择 统一使用Gson作为序列化和反序列化工具，原因如下： 性能对比 * Gson：中等性能，内存占用适中 * Jackson：高性能，但内存占用较大 * Fastjson：高性能，但存在安全漏洞 * Protobuf：最高性能，但需要预编译 功能特性对比 * Gson：* 支持泛型 * * 支持自定义序列化/反序列化 * * 支持循环引用 * * 支持null值处理 * * 支持注解 * * 字段顺序：默认按字母顺序排序，可通过自定义TypeAdapter保持原始顺序 * * null值处理：默认保留null值，可通过配置选择是否序列化null值 * Jackson：* 功能最全面 * * 配置复杂 * * 学习曲线陡峭 * * 字段顺序：默认按字母顺序排序，可通过@JsonPropertyOrder注解控制 * * null值处理：默认保留null值，可通过@JsonInclude注解控制 * Fastjson：* 配置简单 * * 存在安全风险 * * 维护不活跃 * * 字段顺序：默认按字段声明顺序，可通过@JSONField注解控制 * * null值处理：默认不序列化null值，可通过配置修改 * Protobuf：* 性能最好 * * 需要预编译 * * 不支持动态类型 * * 字段顺序：按字段编号顺序，固定不变 * * null值处理：不支持null值，使用默认值代替 使用规范 java RDark solid true true () { # @Override # public void write(JsonWriter out, YourClass value) throws IOException { # out.beginObject(); # // 按原始顺序写入字段 # out.name(\"field1\").value(value.getField1()); # out.name(\"field2\").value(value.getField2()); # // ... 其他字段 # out.endObject(); # } # @Override # public YourClass read(JsonReader in) throws IOException { # YourClass obj = new YourClass(); # in.beginObject(); # while (in.hasNext()) { # switch (in.nextName()) { # case \"field1\": # obj.setField1(in.nextString()); # break; # case \"field2\": # obj.setField2(in.nextString()); # break; # // ... 其他字段 # } # } # in.endObject(); # return obj; # } # }) # .create(); # // 序列化 # String json = gson.toJson(object); # // 反序列化 # Object obj = gson.fromJson(json, Object.class); # ]]&gt; 2. 数值处理规范 2.1 小数位数规范 默认规则 * 所有数值类型默认保留6位小数 * 超过6位小数自动四舍五入 * 禁止使用科学计数法表示 特殊处理 * 金额计算：保留2位小数 * 百分比：保留2位小数 * 经纬度：保留6位小数 * 科学计算：保留6位小数 实现示例 java RDark solid true false 2.2 数值类型规范 金额类型 * 使用BigDecimal * 禁止使用double/float * 统一使用String构造方法 * ```java * // 正确示例 * BigDecimal amount = new BigDecimal(\"123.45\"); // 错误示例 BigDecimal amount = new BigDecimal(123.45); // 可能产生精度问题 ``` 百分比类型 * 使用BigDecimal * 范围：0-100 * 默认保留2位小数 java RDark solid true false 经纬度类型 * 使用BigDecimal * 经度范围：-180到180 * 纬度范围：-90到90 * 保留6位小数 java RDark solid true false = 0 &amp;&amp; # * longitude.compareTo(new BigDecimal(\"180\")) = 0 &amp;&amp; # * latitude.compareTo(new BigDecimal(\"90\")) 3. 异常处理规范 什么时候抛异常，什么时候返回错误码 3.1 异常分类 业务异常 * 继承RuntimeException * 包含错误码和错误信息 * 用于业务逻辑异常 系统异常 * 继承RuntimeException * 用于系统级异常 * 需要记录详细日志 3.2 异常处理示例 4. 记录类型规范 所有企业:ALL 所有连接器:ALL 所有对象：ALL 5. 测试规范 后台项目内测试 命名规范： 单元测试类:   *Test.java   (例如:   UserServiceTest.java ) 集成测试类:   *IT.java   (例如:   UserServiceIT.java ) 单元测试： JUnit 5：测试框架 Mockito：用于创建模拟对象 AssertJ：提供流畅的断言API 集成测试： 常用注解 @SpringBootTest ：加载完整的Spring应用程序上下文 @DataJpaTest ：用于测试JPA组件 @MockBean ：在Spring上下文中添加Mockito模拟 @AutoConfigureMockMvc： 模拟 HTTP 请求和响应。 CI自动化：Maven Surefire插件仅执行 *Test.java，不执行*IT.java 前端项目内测试 功能测试 API测试：使用公司私有部署的Apifox， Apifox 十、资源隔离和限制 1. 速度限制 限速模型为 源数据-&gt;过滤组件-&gt;连接器1-&gt;连接器2-&gt;连接器3 连接器1每分钟处理100条数据，连接器2每分钟处理50条数据，连接器3每分钟处理200条数据. 源数据每分钟发送多少条数据？ 限速策略待补充: 2. 数据大小限制 从外部读到的数据，单词不超过几M ? 打印的日志，单词不超过几M 3. 计算资源隔离 1.1 CPU资源隔离 cpu不做隔离 1.2 内存资源隔离 2. 存储资源隔离 2.1 数据存储隔离 2.2 缓存隔离 十一、系统容量管理 1. 扩容架构图 十二、系统安全性 1. 认证与授权 1.1 用户认证 认证方式 * OAuth2.0认证 * JWT令牌认证 * 双因素认证（2FA） * SSO单点登录 令牌管理 * 访问令牌有效期：2小时 * 刷新令牌有效期：7天 * 令牌自动续期 * 令牌撤销机制 1.2 权限控制 权限模型 * RBAC（基于角色的访问控制） * 集成流管理权限 * 连接器管理权限 * 数据访问权限 * 系统配置权限 1.3 模块功能说明 认证流程 2. 数据安全 2.1 数据加密 2.2 数据隔离 多租户数据隔离 3. 通信安全 3.1 API安全 接口安全* 接口认证 * 请求签名 * 防重放攻击 * 限流控制 3.2 连接器安全 连接器认证 * 支持多种认证方式 * 凭证加密存储 * 定期轮换 * 访问控制 数据传输安全 * 加密传输 * 数据验证 * 完整性校验 * 防篡改机制 4. 审计与监控 4.1 安全审计 审计内容* 用户操作日志 * 系统事件日志 * 安全事件日志 * 访问记录 4.2 安全监控 监控指标 * 认证失败次数 * 异常访问次数 * 系统资源使用 * 安全事件统计 告警机制 * 实时告警 * 告警级别 * 告警通知 * 告警处理 十三、系统高可靠性 十四、系统对账 十五、面向用户的监控 1. 监控架构 2. 监控维度 2.1 业务监控 2.2 性能监控 3. 监控指标 INLINE window.difyChatbotConfig = { token: \"T7LZV1J8Uy7ldXKf\", baseUrl: \"https://dify.firstshare.cn\", systemVariables: {}, userVariables: {}, }; #dify-chatbot-bubble-button { background-color: #1c64f2 !important; } #dify-chatbot-bubble-window { position: fixed !important; right: 40px !important; /* 这里如果要和按钮对齐可调整 */ bottom: 40px !important; z-index: 99999 !important; width: 32rem !important; height: 60rem !important; box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12); } ]]&gt; INLINE ▼ 目录（双击收起） document.addEventListener('DOMContentLoaded', function(){ // 克隆目录（假设页面有class=\"toc-macro\"的目录） var origToc = document.querySelector('.toc-macro'); if(origToc) { var clonedToc = origToc.cloneNode(true); clonedToc.style.margin = '0'; document.getElementById('my_toc_content').appendChild(clonedToc); } // 拖拽 var tocBox = document.getElementById('my_floating_toc'); var handle = document.getElementById('my_drag_handle'); var offsetX, offsetY, dragging = false; handle.onmousedown = function(e) { dragging = true; offsetX = e.clientX - tocBox.offsetLeft; offsetY = e.clientY - tocBox.offsetTop; document.onmousemove = function(e) { if (dragging) { tocBox.style.left = (e.clientX - offsetX) + 'px'; tocBox.style.top = (e.clientY - offsetY) + 'px'; tocBox.style.right = 'auto'; tocBox.style.bottom = 'auto'; } }; document.onmouseup = function() { dragging = false; document.onmousemove = null; document.onmouseup = null; }; }; // 收起/展开 var content = document.getElementById('my_toc_content'); var foldIcon = document.getElementById('my_fold_icon'); var isFolded = false; handle.ondblclick = function(){ isFolded = !isFolded; if(isFolded){ content.style.display = 'none'; foldIcon.style.transform = \"rotate(-90deg)\"; tocBox.style.borderRadius = \"12px 12px 0 0\"; }else{ content.style.display = 'block'; foldIcon.style.transform = \"rotate(0deg)\"; tocBox.style.borderRadius = \"12px\"; } }; // 悬停高亮 tocBox.onmouseenter = function(){ this.style.boxShadow = \"0 8px 32px rgba(0,82,204,0.17)\"; } tocBox.onmouseleave = function(){ this.style.boxShadow = \"0 8px 24px rgba(0,0,0,0.15)\"; } }); ]]&gt;", "contentMarkup": "<p>目录</p><p><ac:structured-macro ac:macro-id=\"72bfb0fd-7dca-463e-b4a3-0bf6b20a1e18\" ac:name=\"toc\" ac:schema-version=\"1\" /></p><p><br /></p><p>UML说明 &nbsp;<a href=\"https://www.visual-paradigm.com/cn/guide/uml-unified-modeling-language/what-is-uml/\">https://www.visual-paradigm.com/cn/guide/uml-unified-modeling-language/what-is-uml/</a></p><p><ac:image ac:alt=\"UML 图表的种类\"><ri:url ri:value=\"https://cdn-images.visual-paradigm.com/guide/what-is-uml/02-uml-diagram-types.png\" /></ac:image></p><h2>一、背景说明</h2><p>iPaaS（Integration Platform as a Service）是一个基于云的集成平台，用于实现系统间的数据流转和业务集成。本平台主要特点：</p><ol><li>提供可视化的集成流设计器</li><li>支持多种集成节点类型</li><li>支持流程自由编排</li><li>基于effektif实现流程引擎</li><li>采用X6作为前端流程设计框架</li></ol><p>需求文档：<a href=\"https://wiki.firstshare.cn/pages/viewpage.action?pageId=422314522\">集成平台-编排集成流 - 深圳研发中心 - 纷享wiki</a></p><p>UI</p><h2>二、架构图</h2><h3>2.1 系统架构图</h3><p><ac:image ac:alt=\"系统架构图\" ac:title=\"系统架构图\"><ri:attachment ri:filename=\"image2025-5-9_15-50-29.png\" /></ac:image></p><p>原图地址 &nbsp;<a href=\"https://365.kdocs.cn/view/l/ccmh4yDjHXLZ?from=docs\">https://365.kdocs.cn/view/l/ccmh4yDjHXLZ?from=docs</a></p><h3>2.2 技术架构图</h3><p><ac:image ac:height=\"400\"><ri:attachment ri:filename=\"image2025-7-11_10-36-39.png\" /></ac:image></p><p>原始链接 <a href=\"https://365.kdocs.cn/view/l/cspzY4lnO93q\">https://365.kdocs.cn/view/l/cspzY4lnO93q</a></p><p><ac:inline-comment-marker ac:ref=\"3544365f-eaf0-407e-b074-fb68376d0042\">技术架构图说明：</ac:inline-comment-marker></p><h2>三、模块划分</h2><h3>1. 前端模块</h3><ul><li>集成流设计器<ul><li>流程可视化设计</li><li>节点配置功能</li><li>集成流管理功能</li><li>节点参数配置</li></ul></li></ul><h3>2. 接入层模块</h3><ul><li>Nginx 路由<ul><li>安全性：ip白名单，并发控制，推送时派发的token&nbsp;</li><li>负载均衡：使用配置中心的ea 分组路由。</li></ul></li><li>CEP 路由。&nbsp; &nbsp;Key统一调整为ERPDSS，三个环境使用同一个key。</li></ul><h3>3. 集成平台基座</h3><ul><li>框架服务。封装和使用其它框架代码，如effektif, 聚合框架等。</li><li>核心服务。数据同步主过程处理。</li><li>基础服务。除了数据流过集成流以外的其它服务。</li></ul><h3>4. &nbsp;数据层模块</h3><ul><li>PostgreSQL</li><li>MongoDB</li><li>RocketMQ</li><li>Redis</li><li>ClickHouse</li></ul><h2>四、流程图</h2><h3>4.1 集成流执行流程图</h3><p><ac:image><ri:attachment ri:filename=\"image2025-4-7_14-41-27.png\" /></ac:image></p><h2>五、时序图</h2><h3>集成流编排(节点执行顺序)</h3><p><ac:image><ri:attachment ri:filename=\"image2025-4-7_14-41-55.png\" /></ac:image></p><h2>六、集成平台基座功能说明</h2><h3>6.1 核心服务</h3><ul><li>集成流管理。准备好上下文和触发集成流的数据，启动集成流。</li><li>租户节点管理<ul><li>节点定义。实现集成流的节点必须要遵循的规范。</li><li>连接器节点管理。 管理访问外部系统的处理节点。</li><li>业务节点管理。除开连接器节点的其它类型节点。</li></ul></li><li>trigger节点管理。获取触发集成流执行的数据。</li><li>Task服务</li><li>Web服务</li><li>鉴权服务</li><li>限速服务</li><li>容错服务</li><li>日志管理</li><li>监控告警</li><li>模板管理</li><li>对账服务</li></ul><h3>6.2 框架服务</h3><ul><li>effektif引擎接口封装。</li><li>聚合分发框架。</li></ul><h3>6.4 集成流定义json示例</h3><ac:structured-macro ac:macro-id=\"a2cecd02-d487-425b-bb13-1b58302376bc\" ac:name=\"code\" ac:schema-version=\"1\"><ac:parameter ac:name=\"theme\">Emacs</ac:parameter><ac:parameter ac:name=\"borderStyle\">solid</ac:parameter><ac:parameter ac:name=\"linenumbers\">true</ac:parameter><ac:plain-text-body><![CDATA[{\n\t\"name\": \"workflow_src1\",\n\t\"activities\": [{\n\t\t\t\"type\": \"startEvent\",\n\t\t\t\"id\": \"start\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"JCActivityParam\",\n\t\t\t\"id\": \"jc1\",\n\t\t\t\"hardyMap\": {\n\t\t\t\t\"numkey\": 100,\n\t\t\t\t\"strkey\": \"val1\"\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"JCActivityParam2\",\n\t\t\t\"id\": \"jc2\"\n\t\t},\n\t\t{\n\t\t\t\"type\": \"endEvent\",\n\t\t\t\"id\": \"end\"\n\t\t}\n\t],\n\t\"transitions\": [{\n\t\t\t\"fromId\": \"start\",\n\t\t\t\"toId\": \"jc1\",\n\t\t\t\"serialNumber\": 0\n\t\t},\n\t\t{\n\t\t\t\"fromId\": \"jc1\",\n\t\t\t\"toId\": \"jc2\",\n\t\t\t\"serialNumber\": 0\n\t\t},\n\t\t{\n\t\t\t\"fromId\": \"jc2\",\n\t\t\t\"toId\": \"end\",\n\t\t\t\"serialNumber\": 0\n\t\t}\n\t],\n\t\"sourceWorkflowId\": \"src1\"\n}]]></ac:plain-text-body></ac:structured-macro><h3>6.5 集成流显示效果示例</h3><p><ac:image><ri:attachment ri:filename=\"image2025-5-7_11-35-51.png\" /></ac:image></p><p>流程说明: jc1和jc2是两个处理节点</p><p><br /></p><h3>6.6 集成流示例的java处理代码</h3><ac:structured-macro ac:macro-id=\"a26f1a59-f567-469f-8cfc-903d62176b37\" ac:name=\"code\" ac:schema-version=\"1\"><ac:parameter ac:name=\"language\">java</ac:parameter><ac:parameter ac:name=\"theme\">RDark</ac:parameter><ac:parameter ac:name=\"borderStyle\">solid</ac:parameter><ac:parameter ac:name=\"linenumbers\">true</ac:parameter><ac:parameter ac:name=\"collapse\">true</ac:parameter><ac:plain-text-body><![CDATA[ public  static Map<String, Object> executeCompleteWorkflow(String workflowJson) {\n        Map<String, Object> result = new HashMap<>();\n        WorkflowInstance instance; \n        System.out.println(\"executeCompleteWorkflow\");       \n        try {\n            TestConfiguration configuration = createConfiguration();                                    \n            WorkflowEngine engine = configuration.getWorkflowEngine();                   \n            System.out.println(\"print engine: \"+engine);\n            WorkflowExecutionListener listener = new CustomWorkflowExecutionListener();\n            ((WorkflowEngineImpl)engine).addWorkflowExecutionListener(listener);           \n            ExecutableWorkflow workflow = new DefaultJsonStreamMapper().readString(workflowJson, ExecutableWorkflow.class);\n            Deployment deployment = engine.deployWorkflow(workflow);\n            result.put(\"deployStatus\", \"success\");\n\n            System.out.println(\"after deployWorkflow\");\n            TriggerInstance trigger = new TriggerInstance(\"testTenantid\").traceId(\"testtraceid\").workflowId(workflow.getId());                       \n            Map<String, Object> conditionMap = Maps.newHashMap();\n            conditionMap.put(\"k1\", \"value1\");\n            conditionMap.put(\"k2\", \"value2\");\n            instance = engine.start(trigger.data(conditionMap));                \n                      \n            // 2. 启动工作流实例          \n            String instanceId = instance.getId().getInternal();\n            System.out.println(\"print instanceId: \"+instanceId);                  \n\n            result.put(\"instanceId\", instanceId);\n            result.put(\"startStatus\", \"success\");                                             \n        } catch (Exception e) {\n            result.put(\"error\", e.getMessage());\n            System.out.println(e);\n        }             \n        return result;\n    }        \n]]></ac:plain-text-body></ac:structured-macro><p><br /></p><h2>七. &nbsp;集成平台基座子功能详细设计-集成流管理</h2><ol><li>子模块的功能描述</li><li>时序图和架构图</li><li>关键的数据结构和方法描述（类图，表结构设计）</li><li>核心场景测试用例-包括功能和压力测试</li></ol><p>准备好上下文和触发集成流的数据，启动集成流。</p><ul><li style=\"list-style-type: none;\"><ul><li>设置元数据：数据的描述metadata，比如数据来源，traceid, tenantid，当前租户的级别，是否每一步都要缓存数据，是否做全流程节点状态监控等。</li><li>切割数据：(1)对于批量数据，拆解为单条，在送给集成流。(2)如果一条数据同时启动多个集成流。需要深度拷贝。</li><li>版本管理。老用户使用老版本，新用户使用新版本，迁移老用户到新版本需要连接器开发者手工触发，平台提供工具。 后面的版本再考虑。</li><li>选中部分节点重放请求。后面版本在考虑。</li></ul></li></ul><h2>八. &nbsp;集成平台基座子功能详细设计-租户节点管理</h2><h3>1.节点定义</h3><p>要实现一个新的节点，必须实现下面两个类。</p><p><span style=\"letter-spacing: 0.0px;\">类图1：集成流页面配置类</span></p><p><ac:image ac:height=\"400\"><ri:attachment ri:filename=\"image2025-5-7_16-18-32.png\" /></ac:image></p><p>类图2：集成流处理节点类</p><p><ac:image ac:height=\"400\"><ri:attachment ri:filename=\"image2025-5-7_16-22-24.png\" /></ac:image></p><h3>2.连接器节点管理</h3><ul><li><h4>trigger和action节点</h4></li></ul><p>一个集成流是一个trigger+一个或多个action组成。集成流的第一个节点只能是trigger节点。</p><ul><li><h4>trigger节点管理</h4></li></ul><p style=\"margin-left: 40.0px;\">Trigger节点的作用是 获取数据，通过轮询，推送方式，监听MQ事件&nbsp; 获取外部数据。trigger节点是集成流中的一个节点，有两种策略执行trigger节点。</p><ul><li style=\"list-style-type: none;\"><ul><li>策略1：启动集成流，顺序执行，第一个节点就是trigger节点，从trigger节点顺序往下执行。</li><li>策略2：trigger节点挑出来，拿到数据后，在启动集成流，执行其它节点。</li></ul></li></ul><p style=\"margin-left: 40.0px;\">策略1更容易理解，所见即所得，但是极难实现。<span style=\"color: rgb(255,102,0);\">因此选择策略2。用户启动集成流以后，trigger节点要统一注册，然后在effektif之外执行轮询和接收推送事件。等数据到达，再启动集成流 。</span><span style=\"color: rgb(255,102,0);\"><span style=\"color: rgb(0,0,0);\">策略1的问题：对于轮询场景，可以定时任务-&gt;启动集成流-&gt;执行轮询，大多数轮询没数据， 但是也启动了集成流。</span></span><span style=\"color: rgb(255,102,0);\"><span style=\"color: rgb(0,0,0);\">对于监听事件的场景，问题更大了。 启动集成流&rarr;监听 要么一直卡住集成流，要么频繁启动集成流进行监听。</span></span></p><p style=\"margin-left: 40.0px;\"><ac:image><ri:attachment ri:filename=\"image2025-5-7_11-13-30.png\" /></ac:image></p><ul><li><h4>trigger接收的批量数据如何传给集成流？</h4></li></ul><p>trigger接收到的数据必须是数组，只支持JSON格式。类似下面的例子，zapier采用的是图2的方法。就是自动拆数据</p><p><ac:image ac:height=\"244\"><ri:attachment ri:filename=\"image2025-5-8_14-37-29.png\" /></ac:image></p><p>策略1</p><p><ac:image><ri:attachment ri:filename=\"image2025-5-8_14-39-17.png\" /></ac:image></p><p>策略2</p><p>策略2对用户的配置来说相对友好一些，但是隐藏一些语义，比如有3条数据，如果第2条失败，第3条要不要继续做。</p><p><br /></p><ul><li><h4>轮询分页支持</h4></li></ul><p><ac:image ac:height=\"400\"><ri:attachment ri:filename=\"image2025-5-8_16-6-11.png\" /></ac:image></p><p>(1)开关控制是否启用分页</p><p>(2)分页占位符支持url param, header, 和自定义函数代码引用。</p><p>(3)分页拉取结束条件为返回记录数为0。</p><p>(4)最多执行xx个分页，控制上限且页面展现。</p><p>执行逻辑流程如下图所示</p><p><ac:image ac:height=\"400\"><ri:attachment ri:filename=\"image2025-5-8_16-16-43.png\" /></ac:image></p><p><br /></p><p>现有连接器，是基于集成平台的接口要求，来提供接口。</p><p>新连接器体系：应该基于<strong>原始接口操作</strong>进行封装，<strong>自动鉴权</strong>，用户<strong>理解原始接口</strong>，则会使用连接器！</p><p>改造思路：</p><p><ac:image ac:height=\"250\" ac:thumbnail=\"true\"><ri:attachment ri:filename=\"集成平台-连接器Hub示意图.png\" /></ac:image></p><p>【金山文档 | WPS云文档】&nbsp;<br /><a href=\"https://365.kdocs.cn/l/ctkcWxeydAQT\">https://365.kdocs.cn/l/ctkcWxeydAQT</a></p><h3>3.节点之间<span>数据传递。</span></h3><p><span>每个节点都能获取到前面所有节点产生的数据。依靠变量实现。</span></p><p><span>每执行到一个节点，把当前节点的输出，都append到变量。可以在集成流lister中实现这个append, 或者在各个处理节点中实现。但处理节点很难确保每个开发者(尤其涉及到外部开发者)都能正确实现。</span></p><p><span><ac:image ac:height=\"400\"><ri:attachment ri:filename=\"image2025-5-8_15-40-37.png\" /></ac:image></span></p><p><br /></p><h3>4.调试功能</h3><p>编辑状态的集成流才可以调试。</p><p>调试数据放在哪里，缓存多久？</p><h3>5.<span style=\"letter-spacing: 0.0px;\">业务节点管理(需要逐个按照功能设计)</span></h3><p><span style=\"letter-spacing: 0.0px;\">除了连接器类型的节点，其它的都归类到业务节点。比如分支，循环，去重，排序。</span></p><ul><li><span style=\"letter-spacing: 0.0px;\">顺序</span></li><li><span style=\"letter-spacing: 0.0px;\">分支</span></li><li><span style=\"letter-spacing: 0.0px;\">循环处理节点</span></li><li><span style=\"letter-spacing: 0.0px;\">字段转换</span></li></ul><p><span style=\"letter-spacing: 0.0px;\"><ac:image ac:height=\"250\"><ri:attachment ri:filename=\"image2025-5-8_16-21-43.png\" /></ac:image></span></p><ul><li>CRM函数处理节点</li><li>异常捕捉</li></ul><p><ac:image ac:height=\"150\"><ri:attachment ri:filename=\"image2025-5-8_15-30-24.png\" /></ac:image></p><ul><li>重试组件</li></ul><p>下面图分别是 anypoint和workato的重试组件，zapier没有找到重试组件。</p><p><ac:image ac:height=\"250\"><ri:attachment ri:filename=\"image2025-5-8_15-33-48.png\" /></ac:image><ac:image ac:height=\"250\" ac:thumbnail=\"true\"><ri:attachment ri:filename=\"image2025-5-8_15-34-25.png\" /></ac:image></p><h2>九. &nbsp;<ac:inline-comment-marker ac:ref=\"7fe06fd9-bc16-4f91-871e-69f99ef48db3\">集成平台基座子功能详细设计-配置管理</ac:inline-comment-marker></h2><h2>十. &nbsp;集成平台基座子功能详细设计-鉴权管理</h2><p>使用专业的，广泛认同的方式。</p><p>基于HTTP的常见授权方式主要有以下几种：</p><ol><li><p><strong>Basic Auth（基本认证）</strong></p><ul><li>基本认证是最简单的HTTP授权方式，在HTTP请求头中使用<code>Authorization: Basic &lt;base64(username:password)&gt;</code>进行认证。</li><li>缺点：凭据明文（base64非加密），安全性较差，通常需配合HTTPS使用。</li></ul></li><li><p><strong>Bearer Token（令牌认证）</strong></p><ul><li>常见于OAuth 2.0中的授权方式，客户端获取到令牌（token）后，在HTTP请求头带上：<code>Authorization: Bearer &lt;token&gt;</code>。</li><li>优点：可设置有效期、权限范围，令牌泄漏危害小于用户名密码。</li></ul></li><li><p><strong>API Key（API密钥认证）</strong></p><ul><li>客户端向服务端申请API Key，访问接口时将API Key放在请求头/URL参数/请求体中，如<code>Authorization: ApiKey &lt;key&gt;</code>或<code>?api_key=xxx</code>。</li><li>多用于B2B服务，但容易被截获，适合简单场景。</li></ul></li><li><p><strong>OAuth 2.0</strong></p><ul><li>一种开放授权协议，主要用于第三方应用访问用户受保护资源。</li><li>授权流程较复杂，常用授权码模式、简化模式、密码模式、客户端凭证模式等。</li><li>通过Bearer Token实现接口访问权限控制。</li></ul></li><li><p><strong>JWT（JSON Web Token）</strong></p><ul><li>JWT是一种基于Token的授权方式，通过签发一个数字签名的Token，客户端保存后每次请求携带。</li><li>一般在Header中：<code>Authorization: Bearer &lt;jwt_token&gt;</code>。</li><li>优势：无状态，服务端不需保存会话信息，既可用作认证又可用作授权。</li></ul></li><li><p><strong>Digest Auth（摘要认证）</strong></p><ul><li>比Basic安全，采用哈希算法加密密码，不直接发送明文密码，仍需注意防护中间人攻击。</li></ul></li></ol><h2>十一. &nbsp;集成平台基座子功能详细设计-限速管理</h2><h2>十二. &nbsp;集成平台基座子功能详细设计-task任务管理</h2><h2>十三. &nbsp;集成平台基座子功能详细设计-web服务管理</h2><h2>十四. &nbsp;集成平台基座子功能详细设计-容错管理</h2><h2>十五、系统设计的规范性</h2><h3>1. 序列化规范</h3><h4>1.1 序列化工具选择</h4><p><ac:inline-comment-marker ac:ref=\"8f9eab39-31ef-44b0-97a9-c941c7f59c1d\">统一使用Gson作为序列化和反序列化工具，原因如下：</ac:inline-comment-marker></p><ol><li>性能对比</li><li>* Gson：中等性能，内存占用适中</li><li>* Jackson：高性能，但内存占用较大</li><li>* Fastjson：高性能，但存在安全漏洞</li><li>* Protobuf：最高性能，但需要预编译</li><li>功能特性对比</li><li>* Gson：* 支持泛型</li><li>* * 支持自定义序列化/反序列化</li><li>* * 支持循环引用</li><li>* * 支持null值处理</li><li>* * 支持注解</li><li>* * 字段顺序：默认按字母顺序排序，可通过自定义TypeAdapter保持原始顺序</li><li>* * null值处理：默认保留null值，可通过配置选择是否序列化null值</li><li>* Jackson：* 功能最全面</li><li>* * 配置复杂</li><li>* * 学习曲线陡峭</li><li>* * 字段顺序：默认按字母顺序排序，可通过@JsonPropertyOrder注解控制</li><li>* * null值处理：默认保留null值，可通过@JsonInclude注解控制</li><li>* Fastjson：* 配置简单</li><li>* * 存在安全风险</li><li>* * 维护不活跃</li><li>* * 字段顺序：默认按字段声明顺序，可通过@JSONField注解控制</li><li>* * null值处理：默认不序列化null值，可通过配置修改</li><li>* Protobuf：* 性能最好</li><li>* * 需要预编译</li><li>* * 不支持动态类型</li><li>* * 字段顺序：按字段编号顺序，固定不变</li><li>* * null值处理：不支持null值，使用默认值代替</li><li>使用规范</li><li><p class=\"auto-cursor-target\"><br /></p><ac:structured-macro ac:macro-id=\"1348e797-fc93-4206-ad60-c0b9670db581\" ac:name=\"code\" ac:schema-version=\"1\"><ac:parameter ac:name=\"language\">java</ac:parameter><ac:parameter ac:name=\"theme\">RDark</ac:parameter><ac:parameter ac:name=\"borderStyle\">solid</ac:parameter><ac:parameter ac:name=\"linenumbers\">true</ac:parameter><ac:parameter ac:name=\"collapse\">true</ac:parameter><ac:plain-text-body><![CDATA[# // 统一配置\n# Gson gson = new GsonBuilder()\n#     .setDateFormat(\"yyyy-MM-dd HH:mm:ss\")\n#     .setNumberFormat(NumberFormat.getInstance())\n#     .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)\n#     .serializeNulls()  // 序列化null值\n#     .create();\n# // 保持字段顺序的配置\n# Gson gson = new GsonBuilder()\n#     .setDateFormat(\"yyyy-MM-dd HH:mm:ss\")\n#     .setNumberFormat(NumberFormat.getInstance())\n#     .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)\n#     .serializeNulls()\n#     .registerTypeAdapter(YourClass.class, new TypeAdapter<YourClass>() {\n#         @Override\n#         public void write(JsonWriter out, YourClass value) throws IOException {\n#             out.beginObject();\n#             // 按原始顺序写入字段\n#             out.name(\"field1\").value(value.getField1());\n#             out.name(\"field2\").value(value.getField2());\n#             // ... 其他字段\n#             out.endObject();\n#         }\n#         @Override\n#         public YourClass read(JsonReader in) throws IOException {\n#             YourClass obj = new YourClass();\n#             in.beginObject();\n#             while (in.hasNext()) {\n#                 switch (in.nextName()) {\n#                     case \"field1\":\n#                         obj.setField1(in.nextString());\n#                         break;\n#                     case \"field2\":\n#                         obj.setField2(in.nextString());\n#                         break;\n#                     // ... 其他字段\n#                 }\n#             }\n#             in.endObject();\n#             return obj;\n#         }\n#     })\n#     .create();\n# // 序列化\n# String json = gson.toJson(object);\n# // 反序列化\n# Object obj = gson.fromJson(json, Object.class);\n# ]]></ac:plain-text-body></ac:structured-macro><p class=\"auto-cursor-target\"><br /></p></li></ol><h3>2. 数值处理规范</h3><h4>2.1 小数位数规范</h4><ol><li>默认规则</li><li>* 所有数值类型默认保留6位小数</li><li>* 超过6位小数自动四舍五入</li><li>* 禁止使用科学计数法表示</li><li>特殊处理</li><li>* 金额计算：保留2位小数</li><li>* 百分比：保留2位小数</li><li>* 经纬度：保留6位小数</li><li>* 科学计算：保留6位小数</li><li>实现示例</li><li><p class=\"auto-cursor-target\"><br /></p><ac:structured-macro ac:macro-id=\"983d0749-3b7f-4de4-9fa9-745cc47c1038\" ac:name=\"code\" ac:schema-version=\"1\"><ac:parameter ac:name=\"language\">java</ac:parameter><ac:parameter ac:name=\"theme\">RDark</ac:parameter><ac:parameter ac:name=\"borderStyle\">solid</ac:parameter><ac:parameter ac:name=\"linenumbers\">true</ac:parameter><ac:parameter ac:name=\"collapse\">false</ac:parameter><ac:plain-text-body><![CDATA[# // 数值格式化工具类\n# public class NumberFormatUtil {\n#     private static final int DEFAULT_SCALE = 6;\n#     public static BigDecimal format(BigDecimal number) {\n#         return format(number, DEFAULT_SCALE);\n#     }\n#     public static BigDecimal format(BigDecimal number, int scale) {\n#         return number.setScale(scale, RoundingMode.HALF_UP);\n#     }\n#     public static String formatToString(BigDecimal number) {\n#         return formatToString(number, DEFAULT_SCALE);\n#     }\n#     public static String formatToString(BigDecimal number, int scale) {\n#         return format(number, scale).stripTrailingZeros().toPlainString();\n#     }\n# }\n# ]]></ac:plain-text-body></ac:structured-macro><p class=\"auto-cursor-target\"><br /></p></li></ol><h4>2.2 数值类型规范</h4><ol><li>金额类型</li><li>* 使用BigDecimal</li><li>* 禁止使用double/float</li><li>* 统一使用String构造方法</li><li>* ```java</li><li>* // 正确示例</li><li>* BigDecimal amount = new BigDecimal(&quot;123.45&quot;);</li><li>// 错误示例</li><li>BigDecimal amount = new BigDecimal(123.45); // 可能产生精度问题</li><li>```</li><li>百分比类型</li><li>* 使用BigDecimal</li><li>* 范围：0-100</li><li><p class=\"auto-cursor-target\">* 默认保留2位小数</p><ac:structured-macro ac:macro-id=\"4bfd6a18-f984-469a-b7fa-ed435b035185\" ac:name=\"code\" ac:schema-version=\"1\"><ac:parameter ac:name=\"language\">java</ac:parameter><ac:parameter ac:name=\"theme\">RDark</ac:parameter><ac:parameter ac:name=\"borderStyle\">solid</ac:parameter><ac:parameter ac:name=\"linenumbers\">true</ac:parameter><ac:parameter ac:name=\"collapse\">false</ac:parameter><ac:plain-text-body><![CDATA[# * // 百分比处理\n# * public static BigDecimal formatPercentage(BigDecimal value) {\n# *   return value.setScale(2, RoundingMode.HALF_UP);\n# * }\n# * ]]></ac:plain-text-body></ac:structured-macro><p class=\"auto-cursor-target\"><br /></p></li><li>经纬度类型</li><li>* 使用BigDecimal</li><li>* 经度范围：-180到180</li><li>* 纬度范围：-90到90</li><li><p class=\"auto-cursor-target\">* 保留6位小数</p><ac:structured-macro ac:macro-id=\"e4502dd1-be27-41a0-b0f4-6e08309e9c76\" ac:name=\"code\" ac:schema-version=\"1\"><ac:parameter ac:name=\"language\">java</ac:parameter><ac:parameter ac:name=\"theme\">RDark</ac:parameter><ac:parameter ac:name=\"borderStyle\">solid</ac:parameter><ac:parameter ac:name=\"linenumbers\">true</ac:parameter><ac:parameter ac:name=\"collapse\">false</ac:parameter><ac:plain-text-body><![CDATA[# * // 经纬度验证\n# * public static boolean isValidCoordinate(BigDecimal longitude, BigDecimal latitude) {\n# *   return longitude.compareTo(new BigDecimal(\"-180\")) >= 0 &&\n# *          longitude.compareTo(new BigDecimal(\"180\")) <= 0 &&\n# *          latitude.compareTo(new BigDecimal(\"-90\")) >= 0 &&\n# *          latitude.compareTo(new BigDecimal(\"90\")) <= 0;\n# * }\n# * ]]></ac:plain-text-body></ac:structured-macro><p class=\"auto-cursor-target\"><br /></p></li></ol><h3>3. 异常处理规范</h3><p>什么时候抛异常，什么时候返回错误码</p><h4>3.1 异常分类</h4><ol><li>业务异常</li><li>* 继承RuntimeException</li><li>* 包含错误码和错误信息</li><li>* 用于业务逻辑异常</li><li>系统异常</li><li>* 继承RuntimeException</li><li>* 用于系统级异常</li><li>* 需要记录详细日志</li></ol><h4>3.2 异常处理示例</h4><h3>4. 记录类型规范</h3><p>所有企业:ALL</p><p>所有连接器:ALL</p><p>所有对象：ALL</p><h3>5. 测试规范</h3><h4>后台项目内测试</h4><p>命名规范：</p><ul style=\"text-align: left;\"><li>单元测试类:<span>&nbsp;</span><span style=\"color: rgb(255,0,0);\"><code>*Test.java</code></span><span>&nbsp;</span>(例如:<span>&nbsp;</span><code>UserServiceTest.java</code>)</li><li>集成测试类:<span>&nbsp;</span><span style=\"color: rgb(255,0,0);\"><code>*IT.java</code></span><span>&nbsp;</span>(例如:<span>&nbsp;</span><code>UserServiceIT.java</code>)</li></ul><p>单元测试：</p><ul><li>JUnit 5：测试框架</li><li>Mockito：用于创建模拟对象</li><li>AssertJ：提供流畅的断言API</li></ul><p>集成测试：</p><p>常用注解</p><ul><li><code>@SpringBootTest</code>：加载完整的Spring应用程序上下文</li><li><code>@DataJpaTest</code>：用于测试JPA组件</li><li><code>@MockBean</code>：在Spring上下文中添加Mockito模拟</li><li>@AutoConfigureMockMvc：<span style=\"color: rgb(26,57,55);\">模拟 HTTP 请求和响应。</span></li></ul><p>CI自动化：Maven Surefire插件仅执行<span style=\"color: rgb(56,58,66);\">*Test.java，不执行*IT.java</span></p><h4><span style=\"color: rgb(56,58,66);\">前端项目内测试</span></h4><h4>功能测试</h4><p>API测试：使用公司私有部署的Apifox，<a href=\"https://apifox.firstshare.cn/web/\">Apifox</a></p><h2>十、资源隔离和限制</h2><h3>1. 速度限制</h3><p>限速模型为</p><p>源数据-&gt;过滤组件-&gt;连接器1-&gt;连接器2-&gt;连接器3</p><p>连接器1每分钟处理100条数据，连接器2每分钟处理50条数据，连接器3每分钟处理200条数据.<br />源数据每分钟发送多少条数据？</p><p>限速策略待补充:</p><h3>2. 数据大小限制</h3><p>从外部读到的数据，单词不超过几M ?<br />打印的日志，单词不超过几M</p><h3>3. 计算资源隔离</h3><h4>1.1 CPU资源隔离</h4><p>cpu不做隔离</p><h4>1.2 内存资源隔离</h4><h3>2. 存储资源隔离</h3><h4>2.1 数据存储隔离</h4><h4>2.2 缓存隔离</h4><p><br /></p><h2>十一、系统容量管理</h2><h3>1. 扩容架构图</h3><p><ac:image><ri:attachment ri:filename=\"image2025-4-7_14-43-30.png\" /></ac:image></p><h2>十二、系统安全性</h2><h3>1. 认证与授权</h3><h4>1.1 用户认证</h4><ol><li>认证方式</li><li>* OAuth2.0认证</li><li>* JWT令牌认证</li><li>* 双因素认证（2FA）</li><li>* SSO单点登录</li><li>令牌管理</li><li>* 访问令牌有效期：2小时</li><li><ac:inline-comment-marker ac:ref=\"10627fd5-3acf-4656-ac4b-c1d19db40816\">* 刷新令牌有效期：7天</ac:inline-comment-marker></li><li>* 令牌自动续期</li><li>* 令牌撤销机制</li></ol><h4>1.2 权限控制</h4><ol><li>权限模型</li><li>* RBAC（基于角色的访问控制）</li><li>* 集成流管理权限</li><li>* 连接器管理权限</li><li>* 数据访问权限</li><li>* 系统配置权限</li></ol><h4>1.3 模块功能说明</h4><ol><li>认证流程<p class=\"auto-cursor-target\"><ac:image><ri:attachment ri:filename=\"image2025-4-7_14-45-53.png\" /></ac:image></p></li></ol><h3>2. 数据安全</h3><h4>2.1 数据加密</h4><h4>2.2 数据隔离</h4><ol><li>多租户数据隔离</li></ol><h3>3. 通信安全</h3><h4>3.1 API安全</h4><ol><li>接口安全* 接口认证</li><li>* 请求签名</li><li>* 防重放攻击</li><li>* 限流控制</li></ol><h4>3.2 连接器安全</h4><ol><li>连接器认证</li><li>* 支持多种认证方式</li><li>* 凭证加密存储</li><li>* 定期轮换</li><li>* 访问控制</li><li>数据传输安全</li><li>* 加密传输</li><li>* 数据验证</li><li>* 完整性校验</li><li>* 防篡改机制</li></ol><h3>4. 审计与监控</h3><h4>4.1 安全审计</h4><ol><li>审计内容* 用户操作日志</li><li>* 系统事件日志</li><li>* 安全事件日志</li><li>* 访问记录</li></ol><h4>4.2 安全监控</h4><ol><li>监控指标</li><li>* 认证失败次数</li><li>* 异常访问次数</li><li>* 系统资源使用</li><li>* 安全事件统计</li><li>告警机制</li><li>* 实时告警</li><li>* 告警级别</li><li>* 告警通知</li><li>* 告警处理</li></ol><h2>十三、系统高可靠性</h2><h2>十四、系统对账</h2><h2>十五、面向用户的监控</h2><h3>1. 监控架构</h3><h3>2. 监控维度</h3><h4>2.1 业务监控</h4><h4>2.2 性能监控</h4><h3>3. 监控指标</h3><p><br /></p><ac:structured-macro ac:macro-id=\"87c91f1d-3e0c-40ce-99d0-e4f2670210e9\" ac:name=\"html-bobswift\" ac:schema-version=\"1\"><ac:parameter ac:name=\"atlassian-macro-output-type\">INLINE</ac:parameter><ac:plain-text-body><![CDATA[<script>\n  window.difyChatbotConfig = {\n    token: \"T7LZV1J8Uy7ldXKf\",\n    baseUrl: \"https://dify.firstshare.cn\",\n    systemVariables: {},\n    userVariables: {},\n  };\n</script>\n<script\n  src=\"https://dify.firstshare.cn/embed.min.js\"\n  id=\"T7LZV1J8Uy7ldXKf\"\n  defer\n></script>\n<style>\n  #dify-chatbot-bubble-button {\n    background-color: #1c64f2 !important;\n  }\n  #dify-chatbot-bubble-window {\n    position: fixed !important;\n    right: 40px !important; /* 这里如果要和按钮对齐可调整 */\n    bottom: 40px !important;\n    z-index: 99999 !important;\n    width: 32rem !important;\n    height: 60rem !important;\n    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);\n  }\n</style>]]></ac:plain-text-body></ac:structured-macro><p class=\"auto-cursor-target\"><br /></p><ac:structured-macro ac:macro-id=\"0e8a2f89-fc71-4847-bfa8-883c8fc00e88\" ac:name=\"html-bobswift\" ac:schema-version=\"1\"><ac:parameter ac:name=\"atlassian-macro-output-type\">INLINE</ac:parameter><ac:plain-text-body><![CDATA[<!-- 悬浮目录容器 -->\n<div id=\"my_floating_toc\" style=\"\n    position: fixed;\n    top: 100px;\n    right: 30px;\n    width: 360px;\n    max-height: 70vh;\n    min-height: 0;\n    background: #fff;\n    border-radius: 12px;\n    box-shadow: 0 8px 24px rgba(0,0,0,0.15);\n    border: 1px solid #eee;\n    z-index: 9999;\n    transition: box-shadow .2s;\n\">\n  <!-- 拖拽把手 -->\n  <div id=\"my_drag_handle\" style=\"\n      cursor: move;\n      padding: 10px 18px;\n      background: #fafafd;\n      border-bottom: 1px solid #f0f0f0;\n      font-weight: bold;\n      border-radius: 12px 12px 0 0;\n      user-select: none;\n      display: flex;\n      align-items: center;\n      \">\n      <span id=\"my_fold_icon\" style=\"transition: transform .2s;margin-right:7px;display:inline-block;\">▼</span>\n      目录（双击收起）\n  </div>\n  <!-- 目录内容区 -->\n  <div id=\"my_toc_content\" style=\"padding:10px;overflow:auto;max-height:55vh;\"></div>\n</div>\n\n<script>\ndocument.addEventListener('DOMContentLoaded', function(){\n\n    // 克隆目录（假设页面有class=\"toc-macro\"的目录）\n    var origToc = document.querySelector('.toc-macro');\n    if(origToc) {\n        var clonedToc = origToc.cloneNode(true);\n        clonedToc.style.margin = '0';\n        document.getElementById('my_toc_content').appendChild(clonedToc);\n    }\n\n    // 拖拽\n    var tocBox = document.getElementById('my_floating_toc');\n    var handle = document.getElementById('my_drag_handle');\n    var offsetX, offsetY, dragging = false;\n    handle.onmousedown = function(e) {\n        dragging = true;\n        offsetX = e.clientX - tocBox.offsetLeft;\n        offsetY = e.clientY - tocBox.offsetTop;\n        document.onmousemove = function(e) {\n            if (dragging) {\n                tocBox.style.left = (e.clientX - offsetX) + 'px';\n                tocBox.style.top = (e.clientY - offsetY) + 'px';\n                tocBox.style.right = 'auto';\n                tocBox.style.bottom = 'auto';\n            }\n        };\n        document.onmouseup = function() {\n            dragging = false;\n            document.onmousemove = null;\n            document.onmouseup = null;\n        };\n    };\n\n    // 收起/展开\n    var content = document.getElementById('my_toc_content');\n    var foldIcon = document.getElementById('my_fold_icon');\n    var isFolded = false;\n    handle.ondblclick = function(){\n        isFolded = !isFolded;\n        if(isFolded){\n            content.style.display = 'none';\n            foldIcon.style.transform = \"rotate(-90deg)\";\n            tocBox.style.borderRadius = \"12px 12px 0 0\";\n        }else{\n            content.style.display = 'block';\n            foldIcon.style.transform = \"rotate(0deg)\";\n            tocBox.style.borderRadius = \"12px\";\n        }\n    };\n\n    // 悬停高亮\n    tocBox.onmouseenter = function(){\n        this.style.boxShadow = \"0 8px 32px rgba(0,82,204,0.17)\";\n    }\n    tocBox.onmouseleave = function(){\n        this.style.boxShadow = \"0 8px 24px rgba(0,0,0,0.15)\";\n    }\n});\n</script>]]></ac:plain-text-body></ac:structured-macro><p class=\"auto-cursor-target\"><br /></p>", "url": "/pages/viewpage.action?pageId=558661747", "version": 115, "createdBy": "冯院华", "updatedBy": "", "images": [{"filename": "image2025-5-9_15-50-29.png"}, {"filename": "image2025-7-11_10-36-39.png"}, {"filename": "image2025-4-7_14-41-27.png"}, {"filename": "image2025-4-7_14-41-55.png"}, {"filename": "image2025-5-7_11-35-51.png"}, {"filename": "image2025-5-7_16-18-32.png"}, {"filename": "image2025-5-7_16-22-24.png"}, {"filename": "image2025-5-7_11-13-30.png"}, {"filename": "image2025-5-8_14-37-29.png"}, {"filename": "image2025-5-8_14-39-17.png"}, {"filename": "image2025-5-8_16-6-11.png"}, {"filename": "image2025-5-8_16-16-43.png"}, {"filename": "集成平台-连接器Hub示意图.png"}, {"filename": "image2025-5-8_15-40-37.png"}, {"filename": "image2025-5-8_16-21-43.png"}, {"filename": "image2025-5-8_15-30-24.png"}, {"filename": "image2025-5-8_15-33-48.png"}, {"filename": "image2025-5-8_15-34-25.png"}, {"filename": "image2025-4-7_14-43-30.png"}, {"filename": "image2025-4-7_14-45-53.png"}], "metadata": {"readTime": "2025-07-22T03:00:49.200Z", "contentLength": 10614, "markdownLength": 13140, "simpleMarkdownLength": 10517, "imageCount": 20, "conversionMethod": "advanced"}}
import sanitizeHtml from 'sanitize-html';
// @ts-ignore
import TurndownService from 'turndown';
// @ts-ignore
import { gfm } from 'turndown-plugin-gfm';
// @ts-ignore
import { confluenceGfm } from 'turndown-plugin-confluence-to-gfm';

/**
 * Utilities for cleaning and transforming Confluence content
 */

/**
 * Extracts plain text from Confluence Storage Format (XHTML)
 * This is a simple implementation - a production version would use a proper HTML parser
 */
export function extractTextFromStorage(storageFormat: string): string {
  if (!storageFormat) return '';

  // Simple regex to strip HTML tags
  return storageFormat
    .replace(/<[^>]*>/g, ' ') // Replace HTML tags with spaces
    .replace(/&[a-z]+;/g, ' ') // Replace HTML entities
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

/**
 * Truncates content to a specified length, preserving word boundaries
 */
export function truncateContent(content: string, maxLength: number = 8000): string {
  if (content.length <= maxLength) return content;

  // Find a good breaking point
  const breakPoint = content.lastIndexOf(' ', maxLength);
  if (breakPoint === -1) return content.substring(0, maxLength);

  return content.substring(0, breakPoint) + '...';
}

/**
 * Optimizes content for AI context windows by removing redundant information
 * and focusing on the most important parts
 */
export function optimizeForAI(content: string): string {
  // This is a placeholder - a real implementation would be more sophisticated
  return truncateContent(content);
}

/**
 * 使用 Turndown 库将 Confluence Storage Format 转换为 Markdown
 * 这个版本能更好地保留图片信息和宏信息
 */
export function storageFormatToMarkdownAdvanced(storageFormat: string, baseUrl?: string): string {
  if (!storageFormat) return '';

  try {
    // 创建 Turndown 服务实例
    const turndownService = new TurndownService({
      headingStyle: 'atx',
      codeBlockStyle: 'fenced',
      bulletListMarker: '-',
      emDelimiter: '*',
      strongDelimiter: '**',
      linkStyle: 'inlined',
      linkReferenceStyle: 'full'
    });

    // 添加 GFM 支持
    turndownService.use(gfm);

    // 添加 Confluence 特定支持
    turndownService.use(confluenceGfm);

    // 自定义规则：保留图片附件信息
    turndownService.addRule('confluenceImages', {
      filter: function (node: any) {
        return node.nodeName === 'AC:IMAGE' ||
               (node.nodeName === 'IMG' && !!node.getAttribute('src'));
      },
      replacement: function (_content: string, node: any) {
        const element = node as Element;

        // 处理 ac:image 标签
        if (element.nodeName === 'AC:IMAGE') {
          const attachment = element.querySelector('ri\\:attachment');
          if (attachment) {
            const filename = attachment.getAttribute('ri:filename');
            const alt = element.getAttribute('ac:alt') || '图片';
            if (filename) {
              const imageUrl = baseUrl ? `${baseUrl}/download/attachments/${filename}` : filename;
              return `![${alt}](${imageUrl} "${filename}")`;
            }
          }
        }

        // 处理普通 img 标签
        if (element.nodeName === 'IMG') {
          const src = element.getAttribute('src');
          const alt = element.getAttribute('alt') || '图片';
          if (src) {
            const fullUrl = src.startsWith('http') ? src : (baseUrl ? `${baseUrl}${src}` : src);
            return `![${alt}](${fullUrl})`;
          }
        }

        return '';
      }
    });

    // 自定义规则：保留宏信息
    turndownService.addRule('confluenceMacros', {
      filter: function (node: any) {
        return node.nodeName === 'AC:STRUCTURED-MACRO';
      },
      replacement: function (content: string, node: any) {
        const element = node as Element;
        const macroName = element.getAttribute('ac:name');

        if (!macroName) return content;

        // 特殊处理代码宏
        if (macroName === 'code') {
          const languageParam = element.querySelector('ac\\:parameter[ac\\:name="language"]');
          const codeBody = element.querySelector('ac\\:plain-text-body');

          if (codeBody) {
            const language = languageParam?.textContent || '';
            const code = codeBody.textContent || '';
            return `\`\`\`${language}\n${code}\n\`\`\``;
          }
        }

        // 特殊处理信息宏
        if (['info', 'note', 'warning', 'tip'].includes(macroName)) {
          const richTextBody = element.querySelector('ac\\:rich-text-body');
          if (richTextBody) {
            const icon = { info: 'ℹ️', note: '📝', warning: '⚠️', tip: '💡' }[macroName] || '📌';
            const bodyContent = turndownService.turndown(richTextBody.innerHTML);
            return `\n> ${icon} **${macroName.toUpperCase()}**\n> ${bodyContent.replace(/\n/g, '\n> ')}\n`;
          }
        }

        // 其他宏保留为注释
        return `\n<!-- Confluence宏: ${macroName} -->\n${content}\n`;
      }
    });

    // 转换为 Markdown
    return turndownService.turndown(storageFormat);

  } catch (error) {
    console.warn('Advanced markdown conversion failed, falling back to simple conversion:', error);
    return storageFormatToMarkdown(storageFormat);
  }
}

/**
 * Converts Confluence Storage Format to Markdown
 * This is a simple implementation - a production version would be more comprehensive
 */
export function storageFormatToMarkdown(storageFormat: string): string {
  if (!storageFormat) return '';

  let markdown = storageFormat;

  // 保留代码块 - 处理 ac:structured-macro name="code"
  markdown = markdown.replace(
    /<ac:structured-macro[^>]*ac:name="code"[^>]*>[\s\S]*?<ac:parameter[^>]*ac:name="language"[^>]*>([^<]*)<\/ac:parameter>[\s\S]*?<ac:plain-text-body><!\[CDATA\[([\s\S]*?)\]\]><\/ac:plain-text-body>[\s\S]*?<\/ac:structured-macro>/g,
    '```$1\n$2\n```'
  );

  // 处理没有语言参数的代码块
  markdown = markdown.replace(
    /<ac:structured-macro[^>]*ac:name="code"[^>]*>[\s\S]*?<ac:plain-text-body><!\[CDATA\[([\s\S]*?)\]\]><\/ac:plain-text-body>[\s\S]*?<\/ac:structured-macro>/g,
    '```\n$1\n```'
  );

  // 保留图片信息 - 转换为Markdown图片格式
  markdown = markdown.replace(
    /<ac:image[^>]*>[\s\S]*?<ri:attachment[^>]*ri:filename="([^"]+)"[^>]*\/>[\s\S]*?<\/ac:image>/g,
    '![图片]($1 "Confluence附件: $1")'
  );

  // 处理普通img标签
  markdown = markdown.replace(
    /<img[^>]*src="([^"]+)"[^>]*alt="([^"]*)"[^>]*>/g,
    '![$2]($1)'
  );
  markdown = markdown.replace(
    /<img[^>]*src="([^"]+)"[^>]*>/g,
    '![图片]($1)'
  );

  // 保留信息宏 - info, note, warning, tip
  markdown = markdown.replace(
    /<ac:structured-macro[^>]*ac:name="(info|note|warning|tip)"[^>]*>[\s\S]*?<ac:rich-text-body>([\s\S]*?)<\/ac:rich-text-body>[\s\S]*?<\/ac:structured-macro>/g,
    (_match: string, type: 'info' | 'note' | 'warning' | 'tip', content: string) => {
      const icon = { info: 'ℹ️', note: '📝', warning: '⚠️', tip: '💡' }[type] || '📌';
      return `\n> ${icon} **${type.toUpperCase()}**\n> ${content.replace(/\n/g, '\n> ')}\n`;
    }
  );

  // 保留表格
  markdown = markdown.replace(/<table[^>]*>([\s\S]*?)<\/table>/g, (_match, tableContent) => {
    // 简单的表格转换 - 可以进一步优化
    let table = tableContent;
    table = table.replace(/<tr[^>]*>([\s\S]*?)<\/tr>/g, '|$1|\n');
    table = table.replace(/<t[hd][^>]*>([\s\S]*?)<\/t[hd]>/g, ' $1 |');
    table = table.replace(/\|\s*\|/g, '|');
    return '\n' + table + '\n';
  });

  // Convert headings
  markdown = markdown.replace(/<h1[^>]*>(.*?)<\/h1>/g, '# $1');
  markdown = markdown.replace(/<h2[^>]*>(.*?)<\/h2>/g, '## $1');
  markdown = markdown.replace(/<h3[^>]*>(.*?)<\/h3>/g, '### $1');
  markdown = markdown.replace(/<h4[^>]*>(.*?)<\/h4>/g, '#### $1');
  markdown = markdown.replace(/<h5[^>]*>(.*?)<\/h5>/g, '##### $1');
  markdown = markdown.replace(/<h6[^>]*>(.*?)<\/h6>/g, '###### $1');

  // Convert paragraphs
  markdown = markdown.replace(/<p[^>]*>(.*?)<\/p>/g, '$1\n\n');

  // Convert bold
  markdown = markdown.replace(/<strong[^>]*>(.*?)<\/strong>/g, '**$1**');
  markdown = markdown.replace(/<b[^>]*>(.*?)<\/b>/g, '**$1**');

  // Convert italic
  markdown = markdown.replace(/<em[^>]*>(.*?)<\/em>/g, '*$1*');
  markdown = markdown.replace(/<i[^>]*>(.*?)<\/i>/g, '*$1*');

  // Convert links
  markdown = markdown.replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/g, '[$2]($1)');

  // Convert lists
  markdown = markdown.replace(/<ul[^>]*>(.*?)<\/ul>/g, '$1\n');
  markdown = markdown.replace(/<ol[^>]*>(.*?)<\/ol>/g, '$1\n');
  markdown = markdown.replace(/<li[^>]*>(.*?)<\/li>/g, '- $1\n');

  // 保留其他重要宏的信息
  markdown = markdown.replace(
    /<ac:structured-macro[^>]*ac:name="([^"]+)"[^>]*>[\s\S]*?<\/ac:structured-macro>/g,
    '\n[Confluence宏: $1]\n'
  );

  // Remove remaining HTML tags
  markdown = sanitizeHtml(markdown, { allowedTags: [], allowedAttributes: {} });

  // Fix spacing
  markdown = markdown.replace(/\n\s*\n/g, '\n\n');

  return markdown.trim();
}

/**
 * 提取 Confluence 存储格式内容中的图片引用（ac:image、img标签等）
 * 返回图片的文件名、src等信息，便于AI识别页面图片
 */
export function extractImageAttachments(xhtml: string): { filename?: string, src?: string }[] {
  const images: { filename?: string, src?: string }[] = [];
  if (!xhtml) return images;
  // 匹配 <ac:image> 标签下的 <ri:attachment ri:filename="..." />
  const acImageRegex = /<ac:image[^>]*>[\s\S]*?<ri:attachment[^>]*ri:filename="([^"]+)"[^>]*\/>[\s\S]*?<\/ac:image>/g;
  let match;
  while ((match = acImageRegex.exec(xhtml)) !== null) {
    images.push({ filename: match[1] });
  }
  // 匹配 <img src="..."> 标签
  const imgRegex = /<img[^>]+src="([^"]+)"[^>]*>/g;
  while ((match = imgRegex.exec(xhtml)) !== null) {
    images.push({ src: match[1] });
  }
  return images;
}


import { ConfluenceApiService } from './src/services/confluence-api';
import { storageFormatToMarkdown, extractImageAttachments } from './src/utils/content-cleaner';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

// 手动加载环境变量
const baseUrl = 'https://wiki.firstshare.cn';
const apiToken = 'NzU3OTA0OTEwMjk1OqUotTzR6Ey9g4xQbZKjcXko+sWp';

/**
 * 测试脚本：读取指定页面ID的内容并生成返回结果到临时文件
 */
async function testReadPage() {
  const pageId = '558661747';

  try {
    console.log(`开始读取页面 ID: ${pageId}`);
    console.log(`使用 Confluence URL: ${baseUrl}`);

    // 初始化 Confluence API
    const confluenceApi = new ConfluenceApiService(baseUrl, apiToken);
    
    // 读取页面内容
    const page = await confluenceApi.getPage(pageId);
    
    console.log(`成功读取页面: ${page.title}`);
    
    // 格式化内容为 Markdown
    const markdownContent = page.content ? storageFormatToMarkdown(page.content) : '';
    
    // 提取图片附件信息
    const images = extractImageAttachments(page.contentMarkup || page.content);
    
    // 构建返回结果对象
    const responseObj = {
      id: page.id,
      title: page.title,
      spaceKey: page.spaceKey,
      content: page.content, // 原始清理后的文本内容
      contentMarkdown: markdownContent, // Markdown格式内容
      contentMarkup: page.contentMarkup, // 原始XHTML标记
      url: page.links.webui,
      version: page.version,
      created: page.created,
      updated: page.updated,
      createdBy: page.createdBy.displayName,
      updatedBy: page.updatedBy.displayName,
      images: images,
      metadata: {
        readTime: new Date().toISOString(),
        contentLength: page.content?.length || 0,
        markdownLength: markdownContent.length,
        imageCount: images.length,
      }
    };
    
    // 生成临时文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `confluence-page-${pageId}-${timestamp}.json`;

    // 确保temp目录存在
    const tempDir = join('.', 'temp');
    try {
      if (!existsSync(tempDir)) {
        mkdirSync(tempDir, { recursive: true });
      }
    } catch (error) {
      console.warn('无法创建temp目录，将保存到当前目录');
    }

    // 写入文件
    const finalPath = join(tempDir, filename);
    writeFileSync(finalPath, JSON.stringify(responseObj, null, 2), 'utf-8');
    
    console.log(`\n✅ 测试完成！`);
    console.log(`📄 页面标题: ${page.title}`);
    console.log(`🔗 页面URL: ${page.links.webui}`);
    console.log(`📊 内容长度: ${page.content?.length || 0} 字符`);
    console.log(`🖼️  图片数量: ${images.length}`);
    console.log(`💾 结果已保存到: ${finalPath}`);
    
    // 同时输出简化的结果到控制台
    console.log(`\n📋 页面内容预览 (前500字符):`);
    console.log('=' .repeat(50));
    console.log(page.content?.substring(0, 500) + (page.content && page.content.length > 500 ? '...' : ''));
    console.log('=' .repeat(50));
    
    return responseObj;
    
  } catch (error) {
    console.error(`❌ 读取页面失败:`, error);
    
    // 记录错误到文件
    const errorObj = {
      pageId,
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
      stack: error instanceof Error ? error.stack : undefined
    };
    
    const errorFilename = `confluence-error-${pageId}-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
    const errorPath = join('temp', errorFilename);

    try {
      writeFileSync(errorPath, JSON.stringify(errorObj, null, 2), 'utf-8');
      console.log(`🚨 错误信息已保存到: ${errorPath}`);
    } catch (writeError) {
      console.error('无法保存错误信息:', writeError);
    }

    throw error;
  }
}

// 直接运行测试
testReadPage()
  .then(() => {
    console.log('\n🎉 测试脚本执行完成');
  })
  .catch((error) => {
    console.error('\n💥 测试脚本执行失败:', error);
  });

export { testReadPage };
